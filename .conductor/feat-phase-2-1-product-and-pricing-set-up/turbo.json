{"$schema": "https://turbo.build/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": ["dist/**"]}, "lint": {"dependsOn": ["^lint"], "inputs": ["src/**/*.{ts,tsx,js,jsx}", ".eslintrc.js", ".es<PERSON><PERSON><PERSON>"], "outputs": []}, "lint:fix": {"dependsOn": ["^lint:fix"], "inputs": ["src/**/*.{ts,tsx,js,jsx}", ".eslintrc.js", ".es<PERSON><PERSON><PERSON>"], "outputs": []}, "format": {"dependsOn": ["^format"], "inputs": ["src/**/*", ".prettier<PERSON>", ".prettieri<PERSON>re"], "outputs": []}, "format:check": {"dependsOn": ["^format:check"], "inputs": ["src/**/*", ".prettier<PERSON>", ".prettieri<PERSON>re"], "outputs": []}, "check-types": {"dependsOn": ["^check-types"]}, "dev": {"cache": false, "persistent": true}, "db:push": {"cache": false, "persistent": true}, "db:studio": {"cache": false, "persistent": true}, "db:migrate": {"cache": false, "persistent": true}, "db:generate": {"cache": false, "persistent": true}}}