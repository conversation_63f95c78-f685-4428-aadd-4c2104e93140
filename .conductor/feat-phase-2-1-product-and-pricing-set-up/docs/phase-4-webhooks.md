# Phase 4: Webhooks & Event Handling

## 🎯 **Phase Status: PENDING** ⏳

**Phase 4.1 Core Webhook Infrastructure**: ⏳ **0/12 tasks complete**

**Total Phase Progress**: 0/12 tasks complete  
**Estimated Time**: 3-4 hours | **Priority**: CRITICAL  
**Dependencies**: [Phases 1-2 Complete](./phase-2-subscriptions.md)

---

## 📋 Phase Overview

This phase implements comprehensive webhook infrastructure for real-time event processing and synchronization between <PERSON><PERSON> and the application. Webhooks are critical for maintaining data consistency, handling asynchronous events, and providing real-time updates to customers.

**Key Learning Objectives:**

- Webhook endpoint security and signature verification
- Event-driven architecture implementation
- Real-time data synchronization
- Event handling and processing strategies
- Webhook reliability and retry mechanisms

---

## 4.1 Core Webhook Infrastructure

**Estimated Time**: 3-4 hours | **Complexity**: PPPP | **Dependencies**: Phases 1-2 complete  
**Status**: ⏳ PENDING

### Server Implementation

- [ ] **STRIPE-055**: Set up webhook endpoint (`POST /api/webhooks/stripe`)
  - [ ] Raw body parsing for signature verification
  - [ ] Stripe signature verification
  - [ ] Event type routing and handling
- [ ] **STRIPE-056**: Implement core event handlers
  - [ ] `payment_intent.succeeded` - Update payment records
  - [ ] `payment_intent.payment_failed` - Handle failed payments
  - [ ] `invoice.payment_succeeded` - Update subscription status
  - [ ] `invoice.payment_failed` - Handle failed subscription payments
  - [ ] `customer.subscription.updated` - Sync subscription changes
  - [ ] `customer.subscription.deleted` - Handle subscription cancellations
- [ ] **STRIPE-057**: Add webhook event logging and monitoring
- [ ] **STRIPE-058**: Implement webhook retry logic and idempotency
- [ ] **STRIPE-059**: Create webhook testing utilities

### Client Implementation

- [ ] **STRIPE-060**: Add real-time payment status updates (WebSocket or polling)
- [ ] **STRIPE-061**: Display webhook event history in admin dashboard
- [ ] **STRIPE-062**: Create customer notifications for payment events

### Testing & Validation

- [ ] **STRIPE-063**: Test webhook locally using Stripe CLI
- [ ] **STRIPE-064**: Test all implemented webhook event types
- [ ] **STRIPE-065**: Validate webhook signature verification
- [ ] **STRIPE-066**: Test webhook failure scenarios and retries

### Implementation Details

#### STRIPE-055: Webhook Endpoint Setup

```typescript
// Webhook endpoint structure:
const webhookEndpoint = {
	path: "/api/webhooks/stripe",
	method: "POST",
	features: {
		signatureVerification: true,
		rawBodyParsing: true,
		eventRouting: true,
		errorHandling: true,
		monitoring: true,
	},
	security: {
		stripeSignature: true,
		timestampValidation: true,
		toleranceWindow: 300, // 5 minutes
		replayProtection: true,
	},
};
```

#### STRIPE-056: Core Event Handlers

```typescript
// Event handler architecture:
const eventHandlers = {
	payment: {
		"payment_intent.succeeded": handlePaymentSuccess,
		"payment_intent.payment_failed": handlePaymentFailure,
		"payment_intent.canceled": handlePaymentCancellation,
		"charge.succeeded": handleChargeSuccess,
		"charge.failed": handleChargeFailure,
		"charge.refunded": handleChargeRefund,
	},
	subscription: {
		"customer.subscription.created": handleSubscriptionCreated,
		"customer.subscription.updated": handleSubscriptionUpdated,
		"customer.subscription.deleted": handleSubscriptionDeleted,
		"invoice.payment_succeeded": handleInvoicePaymentSuccess,
		"invoice.payment_failed": handleInvoicePaymentFailure,
		"invoice.upcoming": handleUpcomingInvoice,
	},
	customer: {
		"customer.created": handleCustomerCreated,
		"customer.updated": handleCustomerUpdated,
		"customer.deleted": handleCustomerDeleted,
	},
};
```

#### STRIPE-057: Webhook Event Logging

```typescript
// Event logging system:
const webhookLogger = {
	structure: {
		eventId: "string",
		eventType: "string",
		timestamp: "datetime",
		processedAt: "datetime",
		processingTime: "number",
		status: "success|failed|retry",
		error: "object|null",
		rawData: "object",
		processedData: "object",
	},
	features: {
		structuredLogging: true,
		performanceMonitoring: true,
		errorTracking: true,
		searchAndFilter: true,
		exportCapabilities: true,
	},
};
```

#### STRIPE-058: Webhook Retry Logic

```typescript
// Retry mechanism:
const retryConfig = {
	maxAttempts: 3,
	backoffStrategy: "exponential",
	initialDelay: 1000, // 1 second
	maxDelay: 300000, // 5 minutes
	jitter: true,
	conditions: {
		retryableErrors: ["network_error", "timeout", "rate_limit"],
		nonRetryableErrors: ["invalid_signature", "invalid_event"],
	},
	tracking: {
		attemptCount: true,
		nextRetryTime: true,
		errorHistory: true,
	},
};
```

### Database Schema for Webhooks

#### Webhook Events Table

```sql
CREATE TABLE webhook_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  stripe_event_id VARCHAR(255) UNIQUE NOT NULL,
  event_type VARCHAR(100) NOT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'pending', -- pending, processed, failed, retry
  processing_attempts INTEGER DEFAULT 0,
  error_message TEXT,
  raw_data JSONB NOT NULL,
  processed_data JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  processed_at TIMESTAMP WITH TIME ZONE,
  metadata JSONB
);

-- Indexes for performance
CREATE INDEX idx_webhook_events_event_type ON webhook_events(event_type);
CREATE INDEX idx_webhook_events_status ON webhook_events(status);
CREATE INDEX idx_webhook_events_created_at ON webhook_events(created_at);
CREATE INDEX idx_webhook_events_stripe_id ON webhook_events(stripe_event_id);
```

#### Webhook Processing Logs

```sql
CREATE TABLE webhook_processing_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  webhook_event_id UUID REFERENCES webhook_events(id),
  attempt_number INTEGER NOT NULL,
  status VARCHAR(20) NOT NULL, -- started, success, failed
  processing_time_ms INTEGER,
  error_message TEXT,
  error_stack TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Security Implementation

#### Signature Verification

```typescript
// Webhook signature verification:
const verifyWebhookSignature = (
	payload: string,
	signature: string,
	secret: string
): boolean => {
	const crypto = require("crypto");

	// Extract timestamp and signature components
	const timestamp = signature.split(",")[0]?.split("=")[1];
	const signatureHash = signature.split(",")[1]?.split("=")[1];

	// Verify timestamp is within tolerance window
	const now = Math.floor(Date.now() / 1000);
	if (Math.abs(now - parseInt(timestamp)) > 300) {
		return false; // Timestamp too old
	}

	// Verify signature
	const expectedSignature = crypto
		.createHmac("sha256", secret)
		.update(`${timestamp}.${payload}`)
		.digest("hex");

	return crypto.timingSafeEqual(
		Buffer.from(signatureHash, "hex"),
		Buffer.from(expectedSignature, "hex")
	);
};
```

### Event Handler Architecture

#### Base Event Handler

```typescript
abstract class BaseEventHandler {
	protected readonly eventType: string;
	protected readonly logger: Logger;

	constructor(eventType: string, logger: Logger) {
		this.eventType = eventType;
		this.logger = logger;
	}

	abstract handle(event: Stripe.Event): Promise<HandlerResult>;

	protected async validateEvent(event: Stripe.Event): Promise<boolean> {
		// Basic event validation
		return !!event.type && !!event.id && !!event.created;
	}

	protected async logProcessing(
		event: Stripe.Event,
		result: HandlerResult,
		processingTime: number
	): Promise<void> {
		this.logger.info("Webhook event processed", {
			eventType: this.eventType,
			eventId: event.id,
			status: result.success ? "success" : "failed",
			processingTime,
			error: result.error,
		});
	}
}
```

#### Specific Event Handler Example

```typescript
class PaymentIntentSuccessHandler extends BaseEventHandler {
	constructor(logger: Logger) {
		super("payment_intent.succeeded", logger);
	}

	async handle(event: Stripe.Event): Promise<HandlerResult> {
		try {
			const paymentIntent = event.data.object as Stripe.PaymentIntent;

			// Update local payment record
			await this.updatePaymentRecord(paymentIntent);

			// Update customer subscription if applicable
			if (paymentIntent.invoice) {
				await this.updateSubscriptionStatus(paymentIntent);
			}

			// Send customer notification
			await this.sendPaymentSuccessNotification(paymentIntent);

			return { success: true };
		} catch (error) {
			this.logger.error("Payment intent success handler failed", {
				error,
				eventId: event.id,
			});
			return { success: false, error: error.message };
		}
	}

	private async updatePaymentRecord(
		paymentIntent: Stripe.PaymentIntent
	): Promise<void> {
		// Implementation for updating payment records
	}

	private async updateSubscriptionStatus(
		paymentIntent: Stripe.PaymentIntent
	): Promise<void> {
		// Implementation for subscription status updates
	}

	private async sendPaymentSuccessNotification(
		paymentIntent: Stripe.PaymentIntent
	): Promise<void> {
		// Implementation for customer notifications
	}
}
```

### Client-Side Real-time Updates

#### WebSocket Integration

```typescript
// Real-time payment status updates:
const usePaymentStatusUpdates = (paymentIntentId: string) => {
	const [status, setStatus] = useState<string>("");
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		const ws = new WebSocket(
			`${process.env.NEXT_PUBLIC_WS_URL}/payments/${paymentIntentId}`
		);

		ws.onmessage = (event) => {
			const data = JSON.parse(event.data);
			setStatus(data.status);

			if (data.error) {
				setError(data.error);
			}
		};

		ws.onerror = (error) => {
			setError("WebSocket connection failed");
			// Fallback to polling
			startPolling();
		};

		return () => ws.close();
	}, [paymentIntentId]);

	const startPolling = () => {
		// Fallback polling implementation
	};

	return { status, error };
};
```

### Testing Strategy

#### STRIPE-063: Local Webhook Testing

```bash
# Stripe CLI webhook forwarding
stripe listen --forward-to localhost:3000/api/webhooks/stripe

# Trigger test events
stripe trigger payment_intent.succeeded
stripe trigger payment_intent.payment_failed
stripe trigger customer.subscription.created
```

#### STRIPE-064: Event Type Testing Matrix

| Event Type                      | Test Scenario        | Expected Outcome                          |
| ------------------------------- | -------------------- | ----------------------------------------- |
| `payment_intent.succeeded`      | Successful payment   | Payment record updated, customer notified |
| `payment_intent.payment_failed` | Card declined        | Payment marked failed, error logged       |
| `invoice.payment_succeeded`     | Subscription payment | Subscription status updated               |
| `invoice.payment_failed`        | Subscription failure | Dunning process initiated                 |
| `customer.subscription.updated` | Plan change          | Local subscription synchronized           |
| `customer.subscription.deleted` | Cancellation         | Subscription marked cancelled             |

---

## 🎯 Phase 4 Learning Outcomes

Upon completion of Phase 4, you will be able to:

### Technical Skills

- Implement secure webhook endpoints with signature verification
- Create event-driven architecture for real-time processing
- Handle various Stripe event types and scenarios
- Implement retry logic and error handling
- Set up webhook testing and monitoring
- Build real-time client updates

### Understanding Concepts

- Webhook security and signature verification
- Event-driven architecture patterns
- Idempotency and retry mechanisms
- Event processing and data synchronization
- Error handling and recovery strategies
- Real-time communication patterns

### Best Practices Implemented

- Secure webhook endpoint implementation
- Comprehensive event handling and logging
- Proper error handling and retry logic
- Real-time customer notifications
- Performance monitoring and optimization
- Testing and validation strategies

---

## 🔗 Phase Dependencies

### Prerequisites

- ✅ **Phase 1 Complete**: Basic payment processing foundation
- ✅ **Phase 2 Complete**: Subscription management system
- ✅ **Database Schema**: Payment and subscription tables
- ✅ **API Infrastructure**: REST endpoints and services

### Integration Points

- **Phase 2 (Subscriptions)**: Subscription event handling
- **Phase 3 (Customer Management)**: Customer event processing
- **Phase 5 (Connect)**: Connect account event handling
- **Phase 7 (Testing)**: Webhook testing and validation

### Critical Dependencies

- Webhooks are essential for real-time data consistency
- Required for subscription management functionality
- Critical for payment status synchronization
- Foundation for advanced features

---

## 📊 Phase 4 Success Metrics

### Technical Metrics

- [ ] All core event handlers implemented and tested
- [ ] Webhook signature verification working
- [ ] Event processing success rate > 99%
- [ ] Retry logic handling failures properly
- [ ] Real-time updates functioning on client
- [ ] Monitoring and logging comprehensive

### Business Metrics

- [ ] Data consistency between Stripe and local DB
- [ ] Real-time customer notification delivery rate
- [ ] Event processing latency < 1 second
- [ ] Error recovery and retry effectiveness
- [ ] Customer satisfaction with real-time updates

### Learning Metrics

- [ ] Understand webhook security best practices
- [ ] Can implement event-driven architecture
- [ ] Know how to handle various event types
- [ ] Understand retry and error handling patterns
- [ ] Can troubleshoot webhook issues

---

## 📁 Expected File Structure

```
apps/server/src/
├── webhooks/
│   ├── handlers/
│   │   ├── base-handler.ts          # Base event handler class
│   │   ├── payment-handlers.ts      # Payment event handlers
│   │   ├── subscription-handlers.ts # Subscription event handlers
│   │   └── customer-handlers.ts     # Customer event handlers
│   ├── middleware/
│   │   ├── signature-verification.ts # Webhook signature verification
│   │   └── event-validation.ts       # Event validation middleware
│   ├── services/
│   │   ├── webhook-service.ts        # Webhook processing service
│   │   ├── event-processor.ts       # Event processing logic
│   │   └── retry-service.ts          # Retry mechanism service
│   └── utils/
│       ├── event-mapper.ts          # Event type mapping
│       └── webhook-tester.ts        # Webhook testing utilities
├── routers/
│   └── webhooks.ts                  # Webhook endpoint router
├── db/schema/
│   └── webhook-events.ts            # Webhook events table schema
└── monitoring/
    ├── webhook-metrics.ts           # Webhook performance metrics
    └── event-monitoring.ts           # Event processing monitoring

apps/web/src/
├── components/
│   ├── real-time/
│   │   ├── payment-updates.tsx       # Real-time payment status
│   │   ├── subscription-updates.tsx  # Real-time subscription status
│   │   └── notification-banner.tsx   # Customer notifications
│   └── admin/
│       └── webhook-monitor.tsx       # Admin webhook monitoring
├── hooks/
│   ├── use-webhook-updates.ts        # Webhook update hooks
│   ├── use-payment-status.ts         # Payment status polling
│   └── use-subscription-status.ts    # Subscription status polling
└── utils/
    ├── websocket-client.ts           # WebSocket client utilities
    └── event-emitter.ts              # Event emitter utilities
```

---

**Previous Phase**: [Phase 3: Customer Management](./phase-3-customer-management.md)  
**Next Phase**: [Phase 5: Stripe Connect](./phase-5-connect.md)  
**Return to Main**: [TODOs.md](./TODOs.md)
