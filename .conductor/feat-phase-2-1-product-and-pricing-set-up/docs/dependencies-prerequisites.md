# Dependencies & Prerequisites

## 📋 Overview

This document outlines the required knowledge, tools, and external dependencies needed to successfully complete the Stripe learning application. Understanding these prerequisites ensures you have the foundation needed to progress through each phase effectively.

---

## Required Knowledge & Skills

### Programming Languages & Frameworks

#### JavaScript/TypeScript (Intermediate Level)

- **Core Concepts**: Variables, functions, objects, arrays, async/await
- **TypeScript**: Types, interfaces, generics, decorators, strict mode
- **ES6+ Features**: Arrow functions, destructuring, modules, promises
- **Error Handling**: Try/catch blocks, custom errors, error propagation
- **Testing**: Unit testing, integration testing, mocking

```typescript
// Example of expected TypeScript proficiency:
interface PaymentIntent {
	id: string;
	amount: number;
	currency: string;
	status: "succeeded" | "failed" | "pending";
	metadata: Record<string, string>;
}

class PaymentService {
	async createPaymentIntent(data: CreatePaymentData): Promise<PaymentIntent> {
		try {
			// Implementation
		} catch (error) {
			throw new PaymentError("Failed to create payment intent", error);
		}
	}
}
```

#### React/Next.js (Basic to Intermediate)

- **React Concepts**: Components, props, state, hooks, context
- **Next.js**: App Router, pages, routing, API routes
- **State Management**: useState, useEffect, useContext, Zustand/Redux
- **Component Lifecycle**: Mounting, updating, unmounting
- **Forms**: Form handling, validation, submission

```typescript
// Example of expected React proficiency:
const PaymentForm: React.FC<{ onSuccess: () => void }> = ({ onSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    setLoading(true);
    try {
      await processPayment();
      onSuccess();
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      {/* Form content */}
    </form>
  );
};
```

#### Node.js/Express (Basic to Intermediate)

- **Node.js**: Event loop, streams, buffers, modules
- **Express**: Middleware, routing, request/response handling
- **REST APIs**: HTTP methods, status codes, RESTful design
- **Middleware**: Authentication, validation, error handling
- **Database**: ORMs, query building, connection management

```typescript
// Example of expected Express proficiency:
import express from "express";
import { body, validationResult } from "express-validator";

const app = express();

app.post(
	"/api/payments",
	[
		body("amount").isNumeric().isInt({ min: 1 }),
		body("currency").isIn(["usd", "eur", "gbp"]),
		body("customer.email").isEmail(),
	],
	async (req, res) => {
		const errors = validationResult(req);
		if (!errors.isEmpty()) {
			return res.status(400).json({ errors: errors.array() });
		}

		try {
			const payment = await createPayment(req.body);
			res.json(payment);
		} catch (error) {
			res.status(500).json({ error: error.message });
		}
	}
);
```

### Database Concepts

#### Relational Database Fundamentals

- **SQL Basics**: SELECT, INSERT, UPDATE, DELETE, JOIN
- **Database Design**: Tables, relationships, normalization, indexes
- **Transactions**: ACID properties, rollback, commit
- **Query Optimization**: Indexes, query planning, performance

#### PostgreSQL (Basic Understanding)

- **PostgreSQL Features**: JSONB support, UUIDs, array types
- **Data Types**: Numeric, text, boolean, timestamps, JSON
- **Advanced Features**: Window functions, CTEs, full-text search

```sql
-- Example of expected SQL proficiency:
CREATE TABLE customers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  name VARCHAR(255) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_customers_email ON customers(email);

SELECT c.id, c.email, COUNT(p.id) as payment_count
FROM customers c
LEFT JOIN payments p ON c.id = p.customer_id
WHERE c.created_at > NOW() - INTERVAL '30 days'
GROUP BY c.id, c.email;
```

### HTTP/REST APIs

#### API Design Principles

- **RESTful Design**: Resources, HTTP methods, status codes
- **API Documentation**: OpenAPI/Swagger, endpoint documentation
- **Versioning**: URL versioning, header versioning
- **Error Handling**: HTTP status codes, error response format
- **Authentication**: JWT, API keys, OAuth2 basics

```typescript
// Example of expected API design proficiency:
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    timestamp: string;
    requestId: string;
  };
}

// API endpoint following REST principles
GET    /api/v1/customers              // List customers
POST   /api/v1/customers              // Create customer
GET    /api/v1/customers/:id          // Get specific customer
PUT    /api/v1/customers/:id          // Update customer
DELETE /api/v1/customers/:id          // Delete customer
```

---

## Development Environment Setup

### System Requirements

#### Hardware Requirements

- **CPU**: 2+ cores recommended
- **Memory**: 8GB RAM minimum, 16GB recommended
- **Storage**: 10GB free space for development
- **Network**: Stable internet connection for API calls

#### Software Requirements

- **Operating System**: macOS, Linux, or Windows (WSL2)
- **Node.js**: Version 18.x or later
- **Package Manager**: npm, yarn, or bun
- **Database**: PostgreSQL 14+ or access to cloud database
- **Git**: Version control for code management

### Development Tools

#### Essential Tools

- **Code Editor**: VS Code with recommended extensions
- **Terminal**: Modern terminal with Git integration
- **Database Client**: DBeaver, pgAdmin, or similar
- **API Testing**: Postman, Insomnia, or curl
- **Version Control**: Git and GitHub/GitLab

#### Recommended VS Code Extensions

```json
{
	"recommendations": [
		"ms-vscode.vscode-typescript-next",
		"esbenp.prettier-vscode",
		"ms-vscode.vscode-json",
		"bradlc.vscode-tailwindcss",
		"ms-vscode.vscode-eslint",
		"ms-python.python",
		"humao.rest-client"
	]
}
```

---

## External Dependencies

### Stripe Account & Services

#### Stripe Account Setup

- **Stripe Account**: Create account at [stripe.com](https://stripe.com)
- **Test Mode**: Use test keys for development
- **Dashboard Access**: Familiarity with Stripe Dashboard
- **API Keys**: Test publishable and secret keys
- **Webhook Endpoints**: Configure webhook endpoints in dashboard

#### Stripe Products & Features

- **Payments**: Payment Intents, Payment Methods
- **Subscriptions**: Products, Prices, Subscriptions
- **Customer Portal**: Self-service customer management
- **Connect**: Marketplace and platform payments
- **Radar**: Fraud detection and prevention

### Database Services

#### PostgreSQL Options

- **Local Development**: PostgreSQL installed locally
- **Cloud Services**:
  - Supabase (recommended for this project)
  - Heroku Postgres
  - Amazon RDS
  - DigitalOcean Managed Databases

#### Database Migration Tools

- **Drizzle ORM**: Database toolkit and migration system
- **Prisma**: Alternative ORM (if preferred)
- **Database Seeds**: Test data generation scripts

### Development & Deployment Tools

#### Package Manager

- **Bun**: Recommended for this project (fastest)
- **npm**: Default Node.js package manager
- **yarn**: Alternative package manager

#### Build Tools

- **Turborepo**: Monorepo management and build optimization
- **TypeScript**: Type checking and compilation
- **ESLint**: Code linting and style enforcement
- **Prettier**: Code formatting and consistency

#### Testing Frameworks

- **Jest**: Unit and integration testing
- **Testing Library**: React component testing
- **Playwright**: End-to-end testing
- **Supertest**: API endpoint testing

### Infrastructure & Hosting

#### Development Infrastructure

- **Local Development**: Docker containers (optional)
- **Environment Management**: .env files and configuration
- **API Tunneling**: ngrok for local webhook testing
- **Monitoring**: Local development monitoring tools

#### Production Considerations

- **Cloud Providers**: Vercel, Netlify, AWS, GCP, Azure
- **Database Hosting**: Managed PostgreSQL services
- **Monitoring**: Application performance monitoring
- **Logging**: Structured logging and aggregation

---

## Phase-Specific Prerequisites

### Phase 1: Foundation & Basic Payments

- **Required**: JavaScript/TypeScript intermediate level
- **Required**: React/Next.js basic understanding
- **Required**: Node.js/Express basic knowledge
- **Required**: Basic database concepts
- **Helpful**: REST API design experience

### Phase 2: Subscription Management

- **Required**: Phase 1 complete
- **Required**: Understanding of subscription business models
- **Required**: Basic financial calculations (proration)
- **Helpful**: Experience with recurring payment systems

### Phase 3: Customer Management

- **Required**: Phase 1 complete
- **Required**: Understanding of customer data management
- **Required**: Basic security and compliance knowledge
- **Helpful**: Experience with user authentication systems

### Phase 4: Webhooks & Event Handling

- **Required**: Phases 1-2 complete
- **Required**: Understanding of event-driven architecture
- **Required**: Basic networking and HTTP knowledge
- **Required**: Stripe CLI for local webhook testing

### Phase 5: Stripe Connect

- **Required**: All previous phases complete
- **Required**: Understanding of marketplace business models
- **Required**: Basic financial and compliance knowledge
- **Required**: Connect account setup in Stripe dashboard

### Phase 6: Advanced Features

- **Required**: Core functionality complete
- **Required**: Understanding of analytics and reporting
- **Required**: International payment concepts
- **Helpful**: Experience with data visualization

### Phase 7: Testing & Quality Assurance

- **Required**: Core features implemented
- **Required**: Understanding of testing methodologies
- **Required**: Security testing concepts
- **Helpful**: Experience with CI/CD pipelines

### Phase 8: Documentation

- **Required**: Implementation complete
- **Required**: Technical writing skills
- **Required**: Understanding of documentation tools
- **Helpful**: Experience with API documentation

### Phase 9: Production Readiness

- **Required**: All features complete and tested
- **Required**: Understanding of production deployment
- **Required**: Monitoring and observability concepts
- **Required**: Security and compliance knowledge

---

## Learning Resources

### Recommended Learning Paths

#### For Beginners

1. **JavaScript Fundamentals**: MDN Web Docs, JavaScript.info
2. **TypeScript Basics**: TypeScript Handbook, official documentation
3. **React Tutorial**: Official React tutorial, React documentation
4. **Node.js Guide**: Node.js official documentation, NodeSchool

#### For Intermediate Developers

1. **Advanced TypeScript**: Advanced TypeScript types, utility types
2. **React Patterns**: React patterns and best practices
3. **Express.js**: Express.js guide, middleware patterns
4. **Database Design**: Database design fundamentals, normalization

#### For Stripe Integration

1. **Stripe Documentation**: [stripe.com/docs](https://stripe.com/docs)
2. **Stripe API Reference**: Complete API documentation
3. **Stripe Blog**: Best practices and new features
4. **Stripe Community**: Developer community and support

### Practice Projects

#### Before Starting

- Build a simple REST API with Express
- Create a React form with validation
- Set up a PostgreSQL database with basic CRUD
- Implement user authentication with JWT

#### Alongside This Project

- Create additional payment method types
- Build advanced analytics dashboards
- Implement complex subscription scenarios
- Add international payment support

---

## Setup Verification Checklist

### Environment Setup

- [ ] Node.js 18+ installed and working
- [ ] PostgreSQL accessible and running
- [ ] Stripe account created with test keys
- [ ] Git configured and working
- [ ] Code editor with extensions installed

### Project Dependencies

- [ ] Clone repository and install dependencies
- [ ] Set up environment variables
- [ ] Run database migrations
- [ ] Start development servers
- [ ] Test basic functionality

### Stripe Integration

- [ ] Stripe CLI installed and configured
- [ ] Test API keys working
- [ ] Webhook endpoints accessible
- [ ] Test cards and scenarios ready
- [ ] Dashboard access verified

---

## Common Issues & Solutions

### Development Environment Issues

#### Node.js Version Compatibility

```bash
# Check Node.js version
node --version

# Use nvm to manage versions
nvm install 18
nvm use 18
```

#### Database Connection Issues

```bash
# Check PostgreSQL status
pg_ctl status

# Test connection
psql -h localhost -U postgres -d stripe_demo
```

#### Stripe CLI Setup

```bash
# Install Stripe CLI
curl -s https://packages.stripe.dev/api/security/cli-install.sh | sh

# Login to Stripe
stripe login

# Test CLI
stripe balance
```

### Dependency Issues

#### Package Installation

```bash
# Clear node modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Or use bun for faster installation
bun install
```

#### TypeScript Issues

```bash
# Clear TypeScript cache
rm -rf .turbo
rm -rf node_modules/.cache

# Rebuild
npm run build
```

---

## Getting Help

### Official Resources

- **Stripe Documentation**: [stripe.com/docs](https://stripe.com/docs)
- **Stripe Support**: [support.stripe.com](https://support.stripe.com)
- **Stripe Community**: [community.stripe.com](https://community.stripe.com)
- **GitHub Issues**: Project repository issues

### Community Resources

- **Stack Overflow**: Stripe-related questions
- **Discord/Slack**: Developer communities
- **Reddit**: r/stripe, r/webdev
- **Dev.to**: Stripe integration tutorials

### Troubleshooting Tips

1. **Check logs**: Always check server and client logs first
2. **Test in isolation**: Test components individually
3. **Use Stripe CLI**: Test webhooks locally
4. **Check environment variables**: Verify all configuration
5. **Review documentation**: Consult Stripe docs regularly

---

**Related Documents:**

- [Learning Checkpoints](./learning-checkpoints.md) - Phase-specific validation questions
- [Success Metrics](./success-metrics.md) - Technical and business metrics
- [Best Practices](./best-practices.md) - Development and production guidance
