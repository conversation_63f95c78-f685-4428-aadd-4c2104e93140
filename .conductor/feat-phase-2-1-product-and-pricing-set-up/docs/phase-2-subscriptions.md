# Phase 2: Subscription Management

## 🎯 **Phase Status: PENDING** ⏳

**Phase 2.1 Product & Pricing Setup**: ⏳ **0/9 tasks complete**  
**Phase 2.2 Subscription Lifecycle Management**: ⏳ **0/9 tasks complete**

**Total Phase Progress**: 0/18 tasks complete  
**Estimated Time**: 7-9 hours | **Priority**: HIGH  
**Dependencies**: [Phase 1 Complete](./phase-1-foundation.md)

---

## 📋 Phase Overview

This phase implements comprehensive subscription management functionality, including product and pricing setup, subscription lifecycle management, and customer self-service capabilities. The focus is on creating a complete recurring payment system with proper proration, upgrades, downgrades, and cancellation handling.

**Key Learning Objectives:**

- Stripe Products and Prices configuration
- Subscription lifecycle management
- Proration calculation and handling
- Customer self-service portals
- Billing cycle management and invoicing

---

## 2.1 Product & Pricing Setup

**Estimated Time**: 3-4 hours | **Complexity**: PPP | **Dependencies**: Phase 1 complete  
**Status**: ⏳ PENDING

### Server Implementation

- [ ] **STRIPE-028**: Create products/plans setup script
  - [ ] Define subscription tiers (Basic, Pro, Enterprise)
  - [ ] Create products in Stripe dashboard programmatically
  - [ ] Set up pricing with different billing cycles
- [ ] **STRIPE-029**: Create plans retrieval endpoint (`GET /api/subscriptions/plans`)
- [ ] **STRIPE-030**: Design and create `subscriptions` database table
  - [ ] `id`, `stripe_subscription_id`, `customer_id`, `plan_id`, `status`, `current_period_start`, `current_period_end`
- [ ] **STRIPE-031**: Create subscription creation endpoint (`POST /api/subscriptions/create`)

### Client Implementation

- [ ] **STRIPE-032**: Build pricing page component with plan comparison
- [ ] **STRIPE-033**: Create subscription checkout component
- [ ] **STRIPE-034**: Implement plan selection and upgrade/downgrade UI
- [ ] **STRIPE-035**: Add billing cycle toggle (monthly/yearly) with discount display

### Implementation Details

#### STRIPE-028: Products/Plans Setup Script

```typescript
// Example subscription tiers to implement:
const subscriptionTiers = {
	basic: {
		name: "Basic",
		description: "Perfect for individuals",
		features: ["10 payments/month", "Basic support", "Standard reporting"],
		prices: {
			monthly: { amount: 999, currency: "usd" },
			yearly: { amount: 9990, currency: "usd", discount: 0.17 }, // 17% discount
		},
	},
	pro: {
		name: "Pro",
		description: "For growing businesses",
		features: ["100 payments/month", "Priority support", "Advanced analytics"],
		prices: {
			monthly: { amount: 2999, currency: "usd" },
			yearly: { amount: 29990, currency: "usd", discount: 0.17 },
		},
	},
	enterprise: {
		name: "Enterprise",
		description: "For large organizations",
		features: [
			"Unlimited payments",
			"24/7 support",
			"Custom analytics",
			"API access",
		],
		prices: {
			monthly: { amount: 9999, currency: "usd" },
			yearly: { amount: 99990, currency: "usd", discount: 0.17 },
		},
	},
};
```

#### STRIPE-030: Subscriptions Database Schema

```sql
-- subscriptions table structure
CREATE TABLE subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  stripe_subscription_id VARCHAR(255) UNIQUE NOT NULL,
  customer_id UUID REFERENCES customers(id),
  plan_id VARCHAR(50) NOT NULL,
  status VARCHAR(50) NOT NULL, -- active, past_due, canceled, unpaid, trialing
  current_period_start TIMESTAMP WITH TIME ZONE NOT NULL,
  current_period_end TIMESTAMP WITH TIME ZONE NOT NULL,
  cancel_at_period_end BOOLEAN DEFAULT FALSE,
  trial_start TIMESTAMP WITH TIME ZONE,
  trial_end TIMESTAMP WITH TIME ZONE,
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### STRIPE-031: Subscription Creation Endpoint

```typescript
// Key features to implement:
- Customer validation and existing subscription checks
- Price selection and proration calculation
- Payment method validation
- Trial period support
- Immediate vs. future subscription start
- Metadata and custom attributes
- Webhook event preparation
```

### Client Components Structure

#### STRIPE-032: Pricing Page Component

- Plan comparison table
- Feature highlighting
- Price toggle (monthly/yearly)
- Call-to-action buttons
- Mobile responsive design

#### STRIPE-033: Subscription Checkout Component

- Selected plan confirmation
- Payment method selection
- Billing information form
- Terms and conditions acceptance
- Order summary and total calculation

---

## 2.2 Subscription Lifecycle Management

**Estimated Time**: 4-5 hours | **Complexity**: PPPP | **Dependencies**: STRIPE-028 to STRIPE-035  
**Status**: ⏳ PENDING

### Server Implementation

- [ ] **STRIPE-036**: Create subscription update endpoint (`PUT /api/subscriptions/:id`)
  - [ ] Plan changes with proration
  - [ ] Billing cycle changes
  - [ ] Quantity modifications
- [ ] **STRIPE-037**: Create subscription cancellation endpoint (`DELETE /api/subscriptions/:id`)
  - [ ] Immediate vs. end-of-period cancellation
  - [ ] Cancellation reasons tracking
- [ ] **STRIPE-038**: Create subscription reactivation endpoint
- [ ] **STRIPE-039**: Implement subscription status checking and synchronization

### Client Implementation

- [ ] **STRIPE-040**: Build customer subscription dashboard
  - [ ] Current plan display
  - [ ] Usage/billing information
  - [ ] Next billing date
  - [ ] Payment method management
- [ ] **STRIPE-041**: Create subscription modification interface
  - [ ] Upgrade/downgrade with preview
  - [ ] Cancellation flow with retention offers
- [ ] **STRIPE-042**: Implement billing history and invoice downloads

### Implementation Details

#### STRIPE-036: Subscription Update Endpoint

```typescript
// Proration handling scenarios:
- Immediate upgrade with prorated charge
- Downgrade at end of current period
- Quantity changes for metered billing
- Billing cycle switching (monthly ↔ yearly)
- Trial period extensions
- Discount/coupon application
- Payment method changes
```

#### STRIPE-037: Subscription Cancellation Logic

```typescript
// Cancellation strategies:
const cancellationStrategies = {
	immediate: {
		description: "Cancel immediately and prorate refund",
		refundUnused: true,
		accessRevoked: true,
	},
	endOfPeriod: {
		description: "Cancel at end of billing period",
		retainAccess: true,
		autoRenewalDisabled: true,
	},
	withRetention: {
		description: "Offer discount to retain customer",
		generateRetentionOffer: true,
		cancellationReasonAnalysis: true,
	},
};
```

#### STRIPE-040: Customer Subscription Dashboard

```typescript
// Dashboard components:
- CurrentPlanCard: Shows active plan, usage, next billing
- BillingHistory: List of invoices and payments
- PaymentMethods: Manage saved payment methods
- PlanChangeInterface: Upgrade/downgrade functionality
- CancellationFlow: Cancel subscription process
- UsageAnalytics: Usage statistics and limits
```

### Testing & Scenarios

- [ ] **STRIPE-043**: Test subscription creation with different plans
- [ ] **STRIPE-044**: Test plan upgrades and downgrades with proration
- [ ] **STRIPE-045**: Test subscription cancellations and reactivations
- [ ] **STRIPE-046**: Test failed payment recovery scenarios

### Test Scenarios Matrix

| Scenario                   | Description                      | Expected Outcome                                             |
| -------------------------- | -------------------------------- | ------------------------------------------------------------ |
| New Subscription           | Customer signs up for Basic plan | Subscription active, first invoice created                   |
| Plan Upgrade               | Basic → Pro mid-cycle            | Prorated charge, immediate access to Pro features            |
| Plan Downgrade             | Pro → Basic at period end        | Access continues until period end, then Basic                |
| Immediate Cancellation     | Cancel subscription immediately  | Access revoked, prorated refund processed                    |
| End-of-Period Cancellation | Cancel at period end             | Access continues until period end                            |
| Failed Payment             | Payment method declined          | Subscription enters past_due state, recovery flow initiated  |
| Trial Period               | Start free trial                 | Trial period set, access granted, card collected after trial |
| Quantity Change            | Modify seat count                | Prorated charge for additional/removed seats                 |

---

## 🎯 Phase 2 Learning Outcomes

Upon completion of Phase 2, you will be able to:

### Technical Skills

- Create and manage Stripe Products and Prices
- Implement subscription lifecycle management
- Handle proration calculations and billing cycles
- Build customer self-service interfaces
- Manage subscription status changes and synchronization
- Implement trial periods and promotional pricing

### Understanding Concepts

- Subscription business models and pricing strategies
- Proration calculation and timing
- Customer retention and cancellation management
- Billing cycles and invoicing workflows
- Trial periods and promotional offers
- Subscription metrics and analytics

### Best Practices Implemented

- Graceful handling of subscription changes
- Clear customer communication for billing changes
- Proper error handling for payment failures
- Comprehensive audit trail for subscription changes
- Customer retention strategies and offers
- Security and compliance for recurring payments

---

## 🔗 Phase Dependencies

### Prerequisites

- ✅ **Phase 1 Complete**: Foundation and basic payment processing
- ✅ **Database Schema**: Customers and payments tables established
- ✅ **API Infrastructure**: Base endpoints and middleware configured
- ✅ **Client Components**: Payment forms and status display ready

### Dependencies for Next Phases

- **Phase 3 (Customer Management)**: Can proceed in parallel
- **Phase 4 (Webhooks)**: Required for subscription event handling
- **Phase 5 (Connect)**: Requires subscription foundation

### Integration Points

- **Webhooks**: Subscription events require webhook handlers (Phase 4)
- **Customer Portal**: Integration with Stripe Customer Portal
- **Invoicing**: Billing history and invoice management
- **Analytics**: Subscription metrics and reporting (Phase 6)

---

## 📊 Phase 2 Success Metrics

### Technical Metrics

- [ ] All subscription endpoints implemented and tested
- [ ] Proration calculations accurate across scenarios
- [ ] Subscription status synchronization working
- [ ] Customer self-service interface functional
- [ ] Billing history and invoice access working
- [ ] Error handling comprehensive for edge cases

### Business Metrics

- [ ] Subscription creation success rate > 95%
- [ ] Customer retention through proper cancellation flows
- [ ] Proration transparency and customer understanding
- [ ] Billing cycle management accuracy
- [ ] Failed payment recovery rate > 80%

### Learning Metrics

- [ ] Understand subscription business models
- [ ] Can implement complex subscription scenarios
- [ ] Know how to handle proration and billing changes
- [ ] Understand customer retention strategies
- [ ] Can troubleshoot subscription issues

---

## 📁 Expected File Structure

```
apps/server/src/
├── services/
│   ├── subscription.service.ts      # Subscription business logic
│   ├── pricing.service.ts           # Product and price management
│   └── billing.service.ts           # Invoicing and billing logic
├── routers/
│   ├── subscriptions.ts             # Subscription API endpoints
│   └── pricing.ts                   # Product and pricing endpoints
├── db/schema/
│   └── subscriptions.ts             # Subscription table schema
└── utils/
    ├── proration.ts                 # Proration calculation utilities
    └── subscription-events.ts       # Subscription event handling

apps/web/src/
├── components/
│   ├── subscription/
│   │   ├── pricing-page.tsx         # Plan comparison and selection
│   │   ├── subscription-checkout.tsx # Subscription signup flow
│   │   ├── subscription-dashboard.tsx # Customer portal
│   │   ├── plan-change.tsx          # Upgrade/downgrade interface
│   │   └── billing-history.tsx      # Invoice history viewer
│   └── hooks/
│       ├── use-subscription.ts      # Subscription data fetching
│       ├── use-pricing.ts           # Pricing data management
│       └── use-billing-history.ts   # Billing history access
└── app/
    ├── pricing/page.tsx             # Public pricing page
    └── subscription/page.tsx         # Customer subscription portal
```

---

**Previous Phase**: [Phase 1: Foundation & Basic Payments](./phase-1-foundation.md)  
**Next Phase**: [Phase 3: Customer Management](./phase-3-customer-management.md)  
**Return to Main**: [TODOs.md](./TODOs.md)
