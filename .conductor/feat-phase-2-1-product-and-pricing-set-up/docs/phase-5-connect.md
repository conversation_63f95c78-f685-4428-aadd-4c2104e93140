# Phase 5: Stripe Connect & Marketplace Features

## 🎯 **Phase Status: PENDING** ⏳

**Phase 5.1 Connect Account Management**: ⏳ **0/15 tasks complete**  
**Phase 5.2 Marketplace Payment Flow**: ⏳ **0/12 tasks complete**  
**Phase 5.3 Connect Compliance & Verification**: ⏳ **0/10 tasks complete**

**Total Phase Progress**: 0/37 tasks complete  
**Estimated Time**: 6-8 hours | **Priority**: HIGH  
**Dependencies**: [Phases 1-4 Complete](./phase-4-webhooks.md)

---

## 📋 Phase Overview

This phase implements Stripe Connect functionality for marketplace and platform features. Stripe Connect enables businesses to accept payments and pay out to third parties, making it ideal for marketplaces, platforms, and software that needs to facilitate payments between multiple parties.

**Key Learning Objectives:**

- Connect account types and onboarding flows
- Marketplace payment processing and fee structures
- Compliance requirements and verification processes
- Payout management and reconciliation
- Platform-specific business models

---

## 5.1 Connect Account Management

**Estimated Time**: 2-3 hours | **Complexity**: HIGH | **Dependencies**: Phases 1-4 complete  
**Status**: ⏳ PENDING

### Connect Account Setup

- [ ] **STRIPE-100**: Design Connect account strategy
  - [ ] Choose account type (Express, Custom, Standard)
  - [ ] Define onboarding flow requirements
  - [ ] Plan compliance and verification processes
  - [ ] Design user experience for connected accounts
- [ ] **STRIPE-101**: Implement Express account onboarding
  - [ ] Create account creation endpoint
  - [ ] Handle account link generation and redirects
  - [ ] Implement account status tracking
  - [ ] Store account information in database
- [ ] **STRIPE-102**: Add Custom account features (if using Custom accounts)
  - [ ] Custom onboarding flow implementation
  - [ ] Capability management and requirements
  - [ ] External account collection and management
- [ ] **STRIPE-103**: Implement account update and management
  - [ ] Profile information updates
  - [ ] Banking information management
  - [ ] Capability enablement and verification
  - [ ] Account deactivation and closure

### Database Schema for Connect

- [ ] **STRIPE-104**: Create connect_accounts table
  - [ ] Account type and status tracking
  - [ ] Capabilities and requirements
  - [ ] Verification status and timeline
  - [ ] Business and personal information
- [ ] **STRIPE-105**: Create external_accounts table
  - [ ] Bank account and card information
  - [ ] Verification status and default settings
  - [ ] Payout eligibility and restrictions
- [ ] **STRIPE-106**: Create account_capabilities table
  - [ ] Capability requirements tracking
  - [ ] Verification status by capability
  - [ ] Timeline and deadline management

### Implementation Details

#### STRIPE-100: Connect Account Strategy

```typescript
// Connect account type decision matrix:
const connectAccountTypes = {
	express: {
		useCase: "Marketplaces with simple onboarding",
		onboarding: "Stripe-hosted, 5-10 minutes",
		compliance: "Stripe handles KYC/KYB",
		capabilities: ["transfers", "card_payments"],
		bestFor: "Most marketplaces, platforms with low complexity",
	},
	custom: {
		useCase: "Complex platforms needing full control",
		onboarding: "Custom flow, 15-30 minutes",
		compliance: "Platform manages KYC/KYB",
		capabilities: ["all_capabilities"],
		bestFor: "Enterprise platforms, regulated industries",
	},
	standard: {
		useCase: "Simple service businesses",
		onboarding: "Direct Stripe signup",
		compliance: "Stripe handles everything",
		capabilities: ["payments_only"],
		bestFor: "Service providers, consultants",
	},
};
```

#### STRIPE-101: Express Account Onboarding

```typescript
// Express account onboarding flow:
const expressOnboardingFlow = {
	steps: [
		"account_creation",
		"account_link_generation",
		"user_redirect_to_stripe",
		"stripe_onboarding_completion",
		"account_verification",
		"capability_assessment",
	],
	endpoints: {
		createAccount: "POST /api/connect/accounts",
		generateLink: "POST /api/connect/accounts/:id/link",
		webhookHandler: "POST /api/webhooks/stripe/connect",
		accountStatus: "GET /api/connect/accounts/:id/status",
	},
	databaseTracking: {
		accountStatus: "pending|active|restricted|suspended",
		capabilities: "transfers|card_payments|legacy_payments",
		requirements: "currently_due|eventually_due|disabled",
	},
};
```

#### STRIPE-104: Connect Accounts Table

```sql
CREATE TABLE connect_accounts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  stripe_account_id VARCHAR(255) UNIQUE NOT NULL,
  account_type VARCHAR(20) NOT NULL CHECK (account_type IN ('express', 'custom', 'standard')),
  status VARCHAR(20) NOT NULL DEFAULT 'pending',
  business_type VARCHAR(20),
  business_name VARCHAR(255),
  business_email VARCHAR(255),
  country VARCHAR(2) NOT NULL,
  default_currency VARCHAR(3) NOT NULL DEFAULT 'usd',
  capabilities JSONB,
  requirements JSONB,
  verification_status JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB
);

-- Indexes for performance
CREATE INDEX idx_connect_accounts_status ON connect_accounts(status);
CREATE INDEX idx_connect_accounts_stripe_id ON connect_accounts(stripe_account_id);
CREATE INDEX idx_connect_accounts_business_type ON connect_accounts(business_type);
CREATE INDEX idx_connect_accounts_country ON connect_accounts(country);
```

---

## 5.2 Marketplace Payment Flow

**Estimated Time**: 2-3 hours | **Complexity**: HIGH | **Dependencies**: Phase 5.1 complete  
**Status**: ⏳ PENDING

### Direct Charge Implementation

- [ ] **STRIPE-107**: Implement direct charge creation
  - [ ] Create payment with destination charge
  - [ ] Handle application fee calculation
  - [ ] Manage transfer group coordination
  - [ ] Implement charge verification and confirmation
- [ ] **STRIPE-108**: Add separate charge and transfer flow
  - [ ] Create charges on platform account
  - [ ] Implement separate transfers to connected accounts
  - [ ] Handle transfer timing and availability
  - [ ] Manage transfer failures and retries
- [ ] **STRIPE-109**: Implement destination charge flow
  - [ ] Create charges with direct destination
  - [ ] Handle application fees on destination charges
  - [ ] Manage payment method restrictions
  - [ ] Implement reconciliation processes

### Fee Management

- [ ] **STRIPE-110**: Design fee structure
  - [ ] Percentage-based fees
  - [ ] Fixed amount fees
  - [ ] Tiered fee structures
  - [ ] Dynamic fee calculation based on factors
- [ ] **STRIPE-111**: Implement fee calculation service
  - [ ] Real-time fee calculation
  - [ ] Fee cap and minimum enforcement
  - [ ] Special fee rules and exceptions
  - [ ] Fee reporting and analytics
- [ ] **STRIPE-112**: Add fee dispute handling
  - [ ] Fee reversal processes
  - [ ] Dispute allocation strategies
  - [ ] Communication flows for disputes
  - [ ] Dispute prevention measures

### Payment Flow Integration

- [ ] **STRIPE-113**: Integrate with existing payment flow
  - [ ] Extend payment creation for marketplace
  - [ ] Add connected account selection
  - [ ] Implement fee display and confirmation
  - [ ] Handle marketplace-specific error cases
- [ ] **STRIPE-114**: Create marketplace dashboard
  - [ ] Connected account management
  - [ ] Transaction history and filtering
  - [ ] Fee revenue reporting
  - [ ] Payout status tracking
- [ ] **STRIPE-115**: Implement payout management
  - [ ] Payout scheduling and timing
  - [ ] Payout failure handling
  - [ ] Manual payout initiation
  - [ ] Payout reconciliation processes

### Implementation Details

#### STRIPE-107: Direct Charge Creation

```typescript
// Direct charge with destination:
const createDirectCharge = async ({
	amount,
	currency,
	paymentMethodId,
	connectedAccountId,
	applicationFee,
	transferGroup,
}: DirectChargeParams) => {
	const paymentIntent = await stripe.paymentIntents.create({
		amount,
		currency,
		payment_method: paymentMethodId,
		confirmation_method: "manual",
		confirm: true,
		transfer_group: transferGroup || `group_${Date.now()}`,
		application_fee_amount: applicationFee,
		transfer_data: {
			destination: connectedAccountId,
		},
		metadata: {
			type: "marketplace_payment",
			connected_account: connectedAccountId,
		},
	});

	return paymentIntent;
};
```

#### STRIPE-111: Fee Calculation Service

```typescript
// Fee calculation logic:
const calculateApplicationFee = (
	amount: number,
	feeStructure: FeeStructure,
	additionalFactors: FeeFactors
): number => {
	let fee = 0;

	// Percentage-based fee
	if (feeStructure.percentage) {
		fee += Math.floor(amount * (feeStructure.percentage / 100));
	}

	// Fixed fee
	if (feeStructure.fixed) {
		fee += feeStructure.fixed;
	}

	// Tiered fees
	if (feeStructure.tiers) {
		for (const tier of feeStructure.tiers) {
			if (
				amount >= tier.minAmount &&
				(tier.maxAmount === undefined || amount <= tier.maxAmount)
			) {
				fee += Math.floor(amount * (tier.rate / 100));
				break;
			}
		}
	}

	// Apply caps
	if (feeStructure.maximumFee) {
		fee = Math.min(fee, feeStructure.maximumFee);
	}

	// Apply minimum
	if (feeStructure.minimumFee) {
		fee = Math.max(fee, feeStructure.minimumFee);
	}

	// Apply additional factors
	if (additionalFactors.riskScore > 0.7) {
		fee = Math.floor(fee * 1.1); // 10% risk premium
	}

	return fee;
};
```

#### STRIPE-115: Payout Management

```typescript
// Payout management system:
const payoutManager = {
	scheduling: {
		automatic: "weekly|biweekly|monthly",
		manual: "on_demand",
		thresholds: {
			minimum: 1000, // $10.00 minimum
			maximum: 1000000, // $10,000.00 maximum per payout
		},
	},
	handling: {
		failures: {
			retry: true,
			maxAttempts: 3,
			backoff: "exponential",
		},
		reconciliation: {
			automatic: true,
			tolerance: 100, // $1.00 tolerance for reconciliation
		},
	},
	monitoring: {
		successRate: ">95%",
		averageProcessingTime: "<3 business days",
		failureAlerts: true,
	},
};
```

---

## 5.3 Connect Compliance & Verification

**Estimated Time**: 2-2.5 hours | **Complexity**: HIGH | **Dependencies**: Phase 5.2 complete  
**Status**: ⏳ PENDING

### Identity Verification

- [ ] **STRIPE-116**: Implement identity verification flow
  - [ ] Document collection and upload
  - [ ] Verification status monitoring
  - [ ] Additional information requests
  - [ ] Verification completion handling
- [ ] **STRIPE-117**: Add business verification processes
  - [ ] Business registration verification
  - [ ] Tax ID verification
  - [ ] Business ownership verification
  - [ ] Industry-specific requirements
- [ ] **STRIPE-118**: Implement ongoing monitoring
  - [ ] Requirement change detection
  - [ ] Deadline management and notifications
  - [ ] Automated verification requests
  - [ ] Compliance status tracking

### Tax Compliance

- [ ] **STRIPE-119**: Implement tax form collection
  - [ ] W-9/W-8BEN form collection
  - [ ] Tax ID validation
  - [ ] Form storage and management
  - [ ] Annual form updates
- [ ] **STRIPE-120**: Add tax reporting features
  - [ ] 1099-K reporting preparation
  - [ ] Tax year end processing
  - [ ] Tax document generation
  - [ ] Tax data export for accounting
- [ ] **STRIPE-121**: Implement tax withholding
  - [ ] Withholding rate calculation
  - [ ] Withholding amount management
  - [ ] Tax payment coordination
  - [ ] Tax reporting integration

### Risk Management

- [ ] **STRIPE-122**: Add risk assessment features
  - [ ] Transaction monitoring
  - [ ] Suspicious activity detection
  - [ ] Risk score calculation
  - [ ] Manual review workflows
- [ ] **STRIPE-123**: Implement dispute management
  - [ ] Dispute escalation procedures
  - [ ] Evidence collection workflows
  - [ ] Dispute communication system
  - [ ] Dispute analytics and reporting
- [ ] **STRIPE-124**: Create compliance dashboard
  - [ ] Verification status overview
  - [ ] Requirement tracking
  - [ ] Risk monitoring
  - [ ] Audit trail and reporting

### Implementation Details

#### STRIPE-116: Identity Verification Flow

```typescript
// Identity verification management:
const identityVerification = {
	documentCollection: {
		acceptedTypes: ["identity_document", "proof_of_address"],
		fileFormats: ["jpg", "png", "pdf"],
		maxSize: 10000000, // 10MB
		verificationMethods: ["manual", "automated"],
	},
	statusTracking: {
		statuses: [
			"not_started",
			"pending",
			"under_review",
			"additional_information_needed",
			"verified",
			"rejected",
		],
		deadlines: {
			initialSubmission: 14, // days
			additionalInfo: 7, // days
			reviewCompletion: 3, // business days
		},
	},
	webhooks: [
		"identity.verification_session.created",
		"identity.verification_session.processing",
		"identity.verification_session.verified",
		"identity.verification_session.requires_input",
	],
};
```

#### STRIPE-119: Tax Form Collection

```typescript
// Tax form management:
const taxFormCollection = {
	forms: {
		us: {
			W9: {
				requiredFor: "US persons",
				fields: [
					"name",
					"business_name",
					"tax_classification",
					"ein",
					"signature",
				],
			},
			W8BEN: {
				requiredFor: "Non-US persons",
				fields: [
					"name",
					"country",
					"foreign_tax_id",
					"beneficial_owner",
					"signature",
				],
			},
		},
	},
	validation: {
		einFormat: /^\d{2}-\d{7}$/,
		ssnFormat: /^\d{3}-\d{2}-\d{4}$/,
		signatureRequired: true,
	},
	storage: {
		encryption: "AES-256",
		retention: "7_years",
		accessControl: "role_based",
	},
};
```

#### STRIPE-122: Risk Assessment

```typescript
// Risk management system:
const riskAssessment = {
	scoring: {
		factors: {
			accountAge: "weight_0.2",
			transactionVolume: "weight_0.3",
			disputeRate: "weight_0.25",
			verificationStatus: "weight_0.15",
			industryRisk: "weight_0.1",
		},
		thresholds: {
			low: 0.3,
			medium: 0.6,
			high: 0.8,
			critical: 0.9,
		},
	},
	monitoring: {
		realtime: {
			transactionPatterns: true,
			velocityChecks: true,
			anomalyDetection: true,
		},
		batch: {
			dailyReviews: true,
			weeklyReports: true,
			monthlyAssessments: true,
		},
	},
	actions: {
		lowRisk: "standard_processing",
		mediumRisk: "enhanced_monitoring",
		highRisk: "manual_review",
		critical: "account_restriction",
	},
};
```

---

## 🎯 Phase 5 Learning Outcomes

Upon completion of Phase 5, you will be able to:

### Technical Skills

- Implement Stripe Connect account management
- Create marketplace payment flows with fee structures
- Handle identity verification and compliance requirements
- Manage tax compliance and reporting
- Implement risk assessment and dispute management
- Build comprehensive marketplace dashboards

### Understanding Concepts

- Connect account types and their use cases
- Marketplace payment processing models
- Compliance requirements for different regions
- Tax obligations and reporting requirements
- Risk management in marketplace environments
- Dispute handling and prevention strategies

### Best Practices Implemented

- Secure Connect account management
- Transparent fee structures and calculations
- Comprehensive compliance monitoring
- Robust identity verification processes
- Effective risk management strategies
- Detailed audit trails and reporting

---

## 🔗 Phase Dependencies

### Prerequisites

- ✅ **Phase 1 Complete**: Basic payment processing foundation
- ✅ **Phase 2 Complete**: Subscription management system
- ✅ **Phase 3 Complete**: Customer management capabilities
- ✅ **Phase 4 Complete**: Webhook and event handling infrastructure

### Integration Points

- **Phase 2 (Subscriptions)**: Marketplace subscription handling
- **Phase 3 (Customer Management)**: Extended account management
- **Phase 4 (Webhooks)**: Connect-specific webhook handling
- **Phase 6 (Advanced Features)**: Advanced marketplace analytics
- **Phase 7 (Testing)**: Connect-specific testing scenarios

### Critical Dependencies

- Connect accounts require solid payment processing foundation
- Marketplace features depend on robust customer management
- Compliance requirements need comprehensive webhook handling
- Tax reporting integrates with advanced analytics

---

## 📊 Phase 5 Success Metrics

### Technical Metrics

- [ ] Connect account onboarding success rate > 80%
- [ ] Payment processing success rate > 98%
- [ ] Fee calculation accuracy 100%
- [ ] Identity verification completion rate > 90%
- [ ] Tax form collection rate > 95%
- [ ] Payout processing success rate > 99%

### Business Metrics

- [ ] Time to onboard connected accounts < 10 minutes
- [ ] Platform revenue from fees tracked accurately
- [ ] Dispute rate < 1% of transactions
- [ ] Compliance violation rate < 0.1%
- [ ] Connected account retention rate > 85%
- [ ] Payout processing time < 3 business days

### Learning Metrics

- [ ] Understand Connect account types and selection criteria
- [ ] Can implement marketplace payment flows
- [ ] Know compliance requirements for different regions
- [ ] Understand tax obligations and reporting
- [ ] Can implement risk management strategies
- [ ] Know dispute handling best practices

---

## 📁 Expected File Structure

```
apps/server/src/
├── connect/
│   ├── services/
│   │   ├── account-service.ts          # Connect account management
│   │   ├── payment-service.ts           # Marketplace payment processing
│   │   ├── fee-service.ts               # Fee calculation and management
│   │   ├── verification-service.ts     # Identity verification
│   │   ├── tax-service.ts               # Tax compliance and reporting
│   │   └── risk-service.ts              # Risk assessment and monitoring
│   ├── handlers/
│   │   ├── account-handlers.ts         # Account creation and management
│   │   ├── payment-handlers.ts          # Marketplace payment processing
│   │   ├── verification-handlers.ts    # Verification workflows
│   │   └── tax-handlers.ts             # Tax form and reporting
│   ├── middleware/
│   │   ├── connect-auth.ts             # Connect-specific authentication
│   │   ├── compliance-check.ts         # Compliance validation
│   │   └── risk-assessment.ts          # Risk evaluation middleware
│   └── utils/
│       ├── fee-calculator.ts           # Fee calculation utilities
│       ├── payout-manager.ts           # Payout scheduling and management
│       └── compliance-monitor.ts       # Compliance monitoring utilities
├── db/schema/
│   ├── connect-accounts.ts             # Connect accounts table
│   ├── external-accounts.ts           # External accounts table
│   ├── account-capabilities.ts         # Account capabilities table
│   ├── tax-forms.ts                    # Tax forms table
│   └── risk-assessments.ts            # Risk assessment table
├── webhooks/
│   └── handlers/
│       ├── connect-handlers.ts        # Connect-specific webhook handlers
│       └── verification-handlers.ts    # Verification event handlers
└── monitoring/
    ├── connect-metrics.ts              # Connect performance metrics
    └── compliance-monitoring.ts        # Compliance monitoring

apps/web/src/
├── components/
│   ├── connect/
│   │   ├── account-onboarding.tsx      # Connected account onboarding
│   │   ├── payment-form.tsx            # Marketplace payment form
│   │   ├── fee-display.tsx             # Fee breakdown display
│   │   └── verification-form.tsx        # Identity verification form
│   ├── admin/
│   │   ├── connect-dashboard.tsx       # Connect account management
│   │   ├── payment-monitoring.tsx      # Transaction monitoring
│   │   ├── compliance-center.tsx       # Compliance management
│   │   └── risk-monitoring.tsx         # Risk assessment dashboard
│   └── dashboard/
│       ├── marketplace-analytics.tsx    # Marketplace analytics
│       ├── revenue-reporting.tsx      # Fee revenue reporting
│       └── payout-management.tsx       # Payout management
├── hooks/
│   ├── use-connect-account.ts          # Connect account management hooks
│   ├── use-marketplace-payment.ts      # Marketplace payment hooks
│   ├── use-verification.ts             # Identity verification hooks
│   └── use-tax-compliance.ts           # Tax compliance hooks
└── utils/
    ├── fee-calculator.ts               # Fee calculation utilities
    ├── payout-scheduler.ts             # Payout scheduling
    └── compliance-helper.ts            # Compliance assistance utilities
```

---

**Previous Phase**: [Phase 4: Webhooks & Event Handling](./phase-4-webhooks.md)  
**Next Phase**: [Phase 6: Advanced Features](./phase-6-advanced-features.md)  
**Return to Main**: [TODOs.md](./TODOs.md)
