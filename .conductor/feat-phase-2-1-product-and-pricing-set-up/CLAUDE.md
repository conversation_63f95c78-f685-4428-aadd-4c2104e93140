# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Stripe payment integration demonstration built with the Better-T-Stack (TypeScript monorepo). The project implements a comprehensive Stripe learning application progressing from basic payments to advanced marketplace features.

**Tech Stack**: Next.js 15 (frontend) + Express.js (backend) + PostgreSQL (via Drizzle ORM) + Stripe API
**Runtime**: Bun.js
**Build System**: Turborepo with parallel task execution
**UI**: TailwindCSS + shadcn/ui components

## Development Commands

### Project Setup

```bash
bun install                    # Install all dependencies
bun db:push                   # Apply database schema changes
bun db:studio                 # Open database studio UI
bun db:generate               # Generate new migrations
bun db:migrate                # Run migrations
bun db:seed                   # Seed database with test data (server only)
```

### Development

```bash
bun dev                       # Start all apps (web on :3001, server on :3000)
bun dev:web                   # Start only Next.js frontend (:3001)
bun dev:server                # Start only Express backend (:3000)
```

### Quality Assurance

```bash
bun check-types              # TypeScript compilation check across all apps
bun lint                     # ESLint check across all apps (max 15 warnings allowed)
bun lint:fix                 # Auto-fix ESLint issues
bun format                   # Format code with Prettier
bun format:check             # Check code formatting
```

### Build & Production

```bash
bun build                    # Build all applications for production
```

### Individual App Commands

```bash
# Server-specific
cd apps/server
bun compile                  # Create optimized binary executable
bun start                    # Start production server

# Web-specific
cd apps/web
bun dev                      # Next.js dev with Turbopack on port 3001
bun build && bun start       # Production build and start
```

## Architecture Overview

### Monorepo Structure

```
stripe-demonstration/
├── apps/
│   ├── web/           # Next.js frontend application
│   └── server/        # Express.js backend API
├── docs/              # Documentation and learning resources
├── turbo.json         # Turborepo task configuration
└── package.json       # Root workspace configuration
```

### Backend Architecture (apps/server)

**Framework**: Express.js with TypeScript
**Database**: PostgreSQL via Drizzle ORM with typed schemas
**Key Libraries**: Stripe SDK, CORS, rate limiting, Zod validation

```
apps/server/src/
├── db/
│   ├── schema/        # Drizzle ORM schemas with Zod validation
│   │   ├── customers.ts    # Customer data with Stripe integration
│   │   ├── payments.ts     # Payment transactions tracking
│   │   └── index.ts        # Schema exports and types
│   ├── migrations/    # Auto-generated database migrations
│   ├── seed.ts       # Database seeding utilities
│   └── index.ts      # Database connection setup
├── lib/
│   └── stripe.ts     # Stripe SDK configuration and utilities
├── middleware/
│   ├── logging.ts    # Request/error logging with data sanitization
│   └── ratelimit.ts  # Rate limiting for payment endpoints
├── routers/          # Express route handlers (to be implemented)
└── index.ts          # Express app setup and configuration
```

### Frontend Architecture (apps/web)

**Framework**: Next.js 15 with App Router
**UI**: TailwindCSS + shadcn/ui components + Lucide icons
**State Management**: TanStack Query for server state, React Context for client state
**Key Libraries**: Stripe.js, React Stripe Elements, next-themes

```
apps/web/src/
├── app/
│   ├── layout.tsx    # Root layout with providers and fonts
│   └── page.tsx      # Landing page
├── components/
│   ├── ui/           # shadcn/ui base components
│   ├── providers/
│   │   └── stripe-provider.tsx  # Stripe Elements provider setup
│   ├── providers.tsx # Combined providers wrapper
│   ├── header.tsx    # App header with theme toggle
│   └── theme-provider.tsx  # Dark/light theme management
├── lib/
│   ├── stripe.ts     # Stripe.js client configuration and utilities
│   ├── api.ts        # Type-safe API client setup
│   └── utils.ts      # Utility functions and class merging
└── index.css         # Global styles and Tailwind imports
```

### Database Schema

**customers table**:

- Links to Stripe customer objects via `stripe_customer_id`
- Stores email, name, phone with validation
- UUID primary keys throughout

**payments table**:

- Tracks Stripe PaymentIntent lifecycle
- Relations to customers table
- Stores amounts in cents, metadata as JSONB
- Status enum matches Stripe's PaymentIntent statuses

## Environment Configuration

### Required Environment Variables

**Server (.env in apps/server/)**:

```
DATABASE_URL=postgresql://...
STRIPE_SECRET_KEY=sk_test_...
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
CORS_ORIGIN=http://localhost:3001
PORT=3000
NODE_ENV=development
```

**Web (.env.local in apps/web/)**:

```
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
```

## Development Patterns

### Database Operations

- All schemas use Drizzle ORM with Zod validation
- Use `drizzle-zod` for automatic schema generation
- Migrations are auto-generated via `bun db:generate`
- Always use typed schemas from `apps/server/src/db/schema/index.ts`

### Stripe Integration Patterns

- Server-side Stripe operations use the configured instance from `apps/server/src/lib/stripe.ts`
- Client-side operations use Stripe Elements via the provider in `apps/web/src/components/providers/stripe-provider.tsx`
- Currency amounts stored in cents (Stripe convention)
- Payment statuses match Stripe PaymentIntent status values exactly

### API Development

- Express routes should implement proper error handling and validation
- Use rate limiting middleware for payment-related endpoints
- Sanitize request data in logging middleware
- Follow RESTful conventions for endpoint design

### Component Development

- Use shadcn/ui components as base building blocks
- Implement proper loading states and error boundaries
- Support both light and dark themes
- Follow accessibility best practices (built into shadcn/ui)

### Code Quality Standards

- TypeScript strict mode enabled
- ESLint configured with max 15 warnings allowed
- Prettier formatting enforced
- Pre-commit hooks via Husky and lint-staged
- Underscore-prefixed variables allowed for unused parameters

## Project Status & Learning Path

This project follows a comprehensive learning plan documented in `docs/TODOs.md`:

**Current Status**: Phase 1.1 Complete - Foundation setup with database, Stripe configuration, and development environment
**Next Phase**: Phase 1.2 - Basic Payment Processing implementation

The learning progression covers:

1. Foundation & Basic Payments ✅ (Environment) → 🔄 (Payment Processing)
2. Subscription Management
3. Customer Management & Payment Methods
4. Webhooks & Event Handling
5. Stripe Connect (Marketplace)
6. Advanced Features & Analytics
7. Testing & Quality Assurance
8. Documentation & Learning Resources
9. Production Readiness

## Key Development Notes

- Bun is used as the package manager and runtime
- Turborepo enables parallel task execution and caching
- Hot reload is enabled for both frontend and backend during development
- Database migrations should be generated after schema changes
- Rate limiting is pre-configured for payment endpoints
- CORS is configured to allow requests from the frontend application
- All sensitive environment variables are validated on startup
