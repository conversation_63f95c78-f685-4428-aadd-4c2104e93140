// API request and response type definitions

export interface CreatePaymentIntentRequest {
	amount: number; // Amount in dollars (will be converted to cents)
	currency: string;
	customer: {
		email: string;
		name?: string;
		phone?: string;
	};
	description?: string;
	metadata?: Record<string, string>;
	automatic_payment_methods?: {
		enabled: boolean;
	};
}

export interface CreatePaymentIntentResponse {
	clientSecret: string;
	paymentIntentId: string;
	customerId: string;
	amount: number; // Amount in cents
	currency: string;
}

export interface ConfirmPaymentRequest {
	paymentIntentId: string;
	paymentMethodId?: string;
}

export interface ConfirmPaymentResponse {
	success: boolean;
	payment: {
		id: string;
		status: string;
		amount: number;
		currency: string;
		customerId: string;
	};
	error?: string;
}

export interface PaymentDetailsResponse {
	id: string;
	stripePaymentIntentId: string;
	amount: number;
	currency: string;
	status: string;
	description?: string;
	receiptEmail?: string;
	paymentMethodId?: string;
	metadata?: Record<string, string>;
	customer: {
		id: string;
		email: string;
		name?: string;
		phone?: string;
	};
	createdAt: string;
	updatedAt: string;
	succeededAt?: string;
	canceledAt?: string;
}

export interface ApiErrorResponse {
	error: string;
	details?: any;
	requestId?: string;
	timestamp: string;
}

// Standard API response wrapper
export interface ApiResponse<T = any> {
	data?: T;
	error?: string;
	message?: string;
	requestId: string;
	timestamp: string;
}
