import "dotenv/config";
import cors from "cors";
import express from "express";
import { requestLogger, errorLogger } from "./middleware/logging";
import { generalRateLimit } from "./middleware/ratelimit";
import { appRouter } from "./routers";

const app = express();

// Trust proxy (important for rate limiting and IP detection)
app.set("trust proxy", 1);

// Apply general rate limiting to all routes
app.use(generalRateLimit);

// CORS configuration
app.use(
	cors({
		origin: process.env.CORS_ORIGIN || "http://localhost:3001",
		methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
		allowedHeaders: ["Content-Type", "Authorization"],
		credentials: true,
	})
);

// Request logging middleware
app.use(requestLogger);

// Body parsing middleware
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get("/", (_req, res) => {
	res.status(200).json({
		status: "ok",
		message: "Stripe Payment Server",
		timestamp: new Date().toISOString(),
		version: "1.0.0",
	});
});

// API routes
app.use("/api", appRouter);

// Error handling middleware (must be last)
app.use(errorLogger);

const port = process.env.PORT || 3000;
app.listen(port, () => {
	console.log(`🚀 Server is running on port ${port}`);
	console.log(`📝 Environment: ${process.env.NODE_ENV || "development"}`);
	console.log(
		`🌐 CORS Origin: ${process.env.CORS_ORIGIN || "http://localhost:3001"}`
	);
});
