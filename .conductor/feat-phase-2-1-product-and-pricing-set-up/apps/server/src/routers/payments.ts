import { Router, type Request, type Response } from "express";
import { z } from "zod";
import { paymentRateLimit, paymentSpeedLimit } from "../middleware/ratelimit";
import { PaymentService } from "../services/payment.service";
// Types imported in schemas below
import {
	sendErrorResponse,
	generateRequestId,
	handleZodError,
	AppError,
} from "../utils/errors";
import {
	checkIdempotencyKey,
	storeIdempotencyResponse,
	paymentIdempotency,
} from "../utils/idempotency";

export const paymentsRouter = Router();

// Apply rate limiting to all payment routes
paymentsRouter.use(paymentRateLimit);
paymentsRouter.use(paymentSpeedLimit);

// Validation schemas
const createPaymentIntentSchema = z.object({
	amount: z
		.number()
		.min(0.5, "Minimum payment amount is $0.50")
		.max(999999.99, "Maximum payment amount is $999,999.99"),
	currency: z.string().length(3, "Currency must be 3 characters").toLowerCase(),
	customer: z.object({
		email: z.string().email("Invalid email address"),
		name: z.string().min(1, "Name is required").optional(),
		phone: z.string().optional(),
	}),
	description: z.string().max(1000, "Description too long").optional(),
	metadata: z.record(z.string(), z.string()).optional(),
	automatic_payment_methods: z
		.object({
			enabled: z.boolean(),
		})
		.optional(),
});

const confirmPaymentSchema = z.object({
	paymentIntentId: z.string().min(1, "Payment intent ID is required"),
	paymentMethodId: z.string().optional(),
});

const paymentIdSchema = z.object({
	id: z.string().uuid("Invalid payment ID format"),
});

// POST /api/payments/create-intent
// Creates a new payment intent with customer handling
paymentsRouter.post("/create-intent", async (req: Request, res: Response) => {
	const requestId = generateRequestId();

	try {
		// Validate request body
		const validatedData = createPaymentIntentSchema.parse(req.body);

		// Check for idempotency key in headers
		const idempotencyKey = req.headers["idempotency-key"] as string;
		let effectiveIdempotencyKey: string | undefined;

		if (idempotencyKey) {
			// Validate idempotency key format
			if (!paymentIdempotency.isValidKey(idempotencyKey)) {
				throw new AppError(
					"Invalid idempotency key format",
					400,
					true,
					requestId
				);
			}

			// Check if this request has been processed before
			const cachedResponse = checkIdempotencyKey(idempotencyKey);
			if (cachedResponse) {
				return res.status(200).json({
					...cachedResponse,
					requestId,
					timestamp: new Date().toISOString(),
				});
			}

			effectiveIdempotencyKey = idempotencyKey;
		} else {
			// Generate idempotency key for Stripe API
			effectiveIdempotencyKey = paymentIdempotency.createStripeIdempotencyKey(
				validatedData.customer.email,
				validatedData.amount,
				"payment_intent"
			);
		}

		// Create payment intent
		const result = await PaymentService.createPaymentIntent(
			validatedData,
			requestId,
			effectiveIdempotencyKey
		);

		const response = {
			data: result,
			message: "Payment intent created successfully",
			requestId,
			timestamp: new Date().toISOString(),
		};

		// Store response for idempotency if key was provided
		if (idempotencyKey) {
			storeIdempotencyResponse(idempotencyKey, response);
		}

		res.status(201).json(response);
	} catch (error) {
		// Handle validation errors
		if (error instanceof z.ZodError) {
			const validationError = handleZodError(error);
			return sendErrorResponse(res, validationError, requestId);
		}

		sendErrorResponse(res, error as Error, requestId);
	}
});

// POST /api/payments/confirm
// Confirms a payment and updates the local database
paymentsRouter.post("/confirm", async (req: Request, res: Response) => {
	const requestId = generateRequestId();

	try {
		// Validate request body
		const validatedData = confirmPaymentSchema.parse(req.body);

		// Confirm payment
		const result = await PaymentService.confirmPayment(
			validatedData,
			requestId
		);

		res.status(200).json({
			data: result,
			message: result.success
				? "Payment confirmed successfully"
				: "Payment confirmation failed",
			requestId,
			timestamp: new Date().toISOString(),
		});
	} catch (error) {
		// Handle validation errors
		if (error instanceof z.ZodError) {
			const validationError = handleZodError(error);
			return sendErrorResponse(res, validationError, requestId);
		}

		sendErrorResponse(res, error as Error, requestId);
	}
});

// GET /api/payments/:id
// Retrieves payment details with customer information
paymentsRouter.get("/:id", async (req: Request, res: Response) => {
	const requestId = generateRequestId();

	try {
		// Validate payment ID parameter
		const { id } = paymentIdSchema.parse(req.params);

		// Get payment details
		const result = await PaymentService.getPaymentDetails(id, requestId);

		res.status(200).json({
			data: result,
			message: "Payment details retrieved successfully",
			requestId,
			timestamp: new Date().toISOString(),
		});
	} catch (error) {
		// Handle validation errors
		if (error instanceof z.ZodError) {
			const validationError = handleZodError(error);
			return sendErrorResponse(res, validationError, requestId);
		}

		sendErrorResponse(res, error as Error, requestId);
	}
});

// GET /api/payments/customer/:customerId
// Get payment history for a specific customer
paymentsRouter.get(
	"/customer/:customerId",
	async (req: Request, res: Response) => {
		const requestId = generateRequestId();

		try {
			// Validate customer ID
			const customerIdSchema = z.object({
				customerId: z.string().uuid("Invalid customer ID format"),
			});
			const { customerId } = customerIdSchema.parse(req.params);

			// Parse query parameters
			const querySchema = z.object({
				limit: z
					.string()
					.transform(Number)
					.pipe(z.number().min(1).max(100))
					.optional(),
				offset: z.string().transform(Number).pipe(z.number().min(0)).optional(),
			});

			const queryParams = querySchema.parse(req.query);

			// Get customer payments
			const result = await PaymentService.getCustomerPayments(
				customerId,
				requestId,
				queryParams
			);

			res.status(200).json({
				data: result,
				message: "Customer payment history retrieved successfully",
				requestId,
				timestamp: new Date().toISOString(),
			});
		} catch (error) {
			// Handle validation errors
			if (error instanceof z.ZodError) {
				const validationError = handleZodError(error);
				return sendErrorResponse(res, validationError, requestId);
			}

			sendErrorResponse(res, error as Error, requestId);
		}
	}
);

// POST /api/payments/:id/cancel
// Cancel a payment intent
paymentsRouter.post("/:id/cancel", async (req: Request, res: Response) => {
	const requestId = generateRequestId();

	try {
		// Validate payment ID
		const { id } = paymentIdSchema.parse(req.params);

		// Cancel payment
		const result = await PaymentService.cancelPayment(id, requestId);

		res.status(200).json({
			data: {
				id: result.id,
				status: result.status,
				canceledAt: result.canceledAt?.toISOString(),
			},
			message: "Payment canceled successfully",
			requestId,
			timestamp: new Date().toISOString(),
		});
	} catch (error) {
		// Handle validation errors
		if (error instanceof z.ZodError) {
			const validationError = handleZodError(error);
			return sendErrorResponse(res, validationError, requestId);
		}

		sendErrorResponse(res, error as Error, requestId);
	}
});

// paymentsRouter is already exported above
