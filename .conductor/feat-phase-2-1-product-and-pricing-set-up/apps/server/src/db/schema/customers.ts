import { pgTable, text, timestamp, uuid } from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { z } from "zod";

// Customers table for storing customer information
export const customers = pgTable("customers", {
	id: uuid("id").primaryKey().defaultRandom(),
	stripeCustomerId: text("stripe_customer_id").unique().notNull(),
	email: text("email").notNull(),
	name: text("name"),
	phone: text("phone"),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Zod schemas for validation
export const insertCustomerSchema = createInsertSchema(customers, {
	email: z.string().email("Invalid email address"),
	name: z.string().min(1, "Name is required").optional(),
	phone: z.string().optional(),
	stripeCustomerId: z.string().min(1, "Stripe customer ID is required"),
});

export const selectCustomerSchema = createSelectSchema(customers);
export const updateCustomerSchema = insertCustomerSchema
	.partial()
	.omit({ id: true });

// Types
export type Customer = typeof customers.$inferSelect;
export type NewCustomer = typeof customers.$inferInsert;
export type UpdateCustomer = z.infer<typeof updateCustomerSchema>;
