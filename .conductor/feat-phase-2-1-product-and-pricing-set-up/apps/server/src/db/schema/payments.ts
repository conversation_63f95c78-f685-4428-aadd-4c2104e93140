import { relations } from "drizzle-orm";
import {
	pgTable,
	text,
	timestamp,
	uuid,
	integer,
	jsonb,
} from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { z } from "zod";
import { customers } from "./customers";

// Payment status enum values matching Stripe's PaymentIntent status
export const paymentStatusEnum = [
	"requires_payment_method",
	"requires_confirmation",
	"requires_action",
	"processing",
	"succeeded",
	"canceled",
	"requires_capture",
] as const;

// Payments table for tracking payment transactions
export const payments = pgTable("payments", {
	id: uuid("id").primaryKey().defaultRandom(),
	stripePaymentIntentId: text("stripe_payment_intent_id").unique().notNull(),
	customerId: uuid("customer_id")
		.references(() => customers.id)
		.notNull(),
	amount: integer("amount").notNull(), // Amount in cents
	currency: text("currency").notNull().default("usd"),
	status: text("status", { enum: paymentStatusEnum }).notNull(),
	paymentMethodId: text("payment_method_id"),
	receiptEmail: text("receipt_email"),
	description: text("description"),
	metadata: jsonb("metadata"), // Store additional payment metadata
	clientSecret: text("client_secret"), // Store for frontend use
	canceledAt: timestamp("canceled_at"),
	succeededAt: timestamp("succeeded_at"),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Relations
export const paymentsRelations = relations(payments, ({ one }) => ({
	customer: one(customers, {
		fields: [payments.customerId],
		references: [customers.id],
	}),
}));

export const customersRelations = relations(customers, ({ many }) => ({
	payments: many(payments),
}));

// Zod schemas for validation
export const insertPaymentSchema = createInsertSchema(payments, {
	amount: z.number().min(50, "Minimum payment amount is $0.50"), // Stripe minimum
	currency: z.string().length(3, "Currency must be 3 characters"),
	status: z.enum(paymentStatusEnum),
	receiptEmail: z.string().email("Invalid email address").optional(),
	description: z.string().max(1000, "Description too long").optional(),
	stripePaymentIntentId: z
		.string()
		.min(1, "Stripe payment intent ID is required"),
	customerId: z.string().uuid("Invalid customer ID"),
	paymentMethodId: z.string().optional(),
	metadata: z.record(z.string(), z.string()).optional(),
});

export const selectPaymentSchema = createSelectSchema(payments);
export const updatePaymentSchema = insertPaymentSchema
	.partial()
	.omit({ id: true });

// Types
export type Payment = typeof payments.$inferSelect;
export type NewPayment = typeof payments.$inferInsert;
export type UpdatePayment = z.infer<typeof updatePaymentSchema>;
export type PaymentStatus = (typeof paymentStatusEnum)[number];

// Helper types for API responses
export interface PaymentWithCustomer extends Payment {
	customer: {
		id: string;
		email: string;
		name: string | null;
	};
}
