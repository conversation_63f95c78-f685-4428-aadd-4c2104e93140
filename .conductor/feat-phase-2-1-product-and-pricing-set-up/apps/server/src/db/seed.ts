import "dotenv/config";
import { customers, payments } from "./schema";
import { db } from "./index";

async function seed() {
	console.log("🌱 Starting database seeding...");

	try {
		// Clear existing data (be careful in production!)
		console.log("🗑️  Clearing existing data...");
		await db.delete(payments);
		await db.delete(customers);

		// Seed test customers
		console.log("👥 Seeding test customers...");
		const testCustomers = await db
			.insert(customers)
			.values([
				{
					stripeCustomerId: "cus_test_001",
					email: "<EMAIL>",
					name: "<PERSON>",
					phone: "+1234567890",
				},
				{
					stripeCustomerId: "cus_test_002",
					email: "<EMAIL>",
					name: "<PERSON>",
				},
				{
					stripeCustomerId: "cus_test_003",
					email: "<EMAIL>",
					name: "Test User",
					phone: "+1987654321",
				},
			])
			.returning();

		console.log(`✅ Created ${testCustomers.length} test customers`);

		// Seed test payments
		console.log("💰 Seeding test payments...");
		const testPayments = await db
			.insert(payments)
			.values([
				{
					stripePaymentIntentId: "pi_test_001",
					customerId: testCustomers[0].id,
					amount: 2999, // $29.99
					currency: "usd",
					status: "succeeded",
					description: "Test successful payment",
					receiptEmail: testCustomers[0].email,
					succeededAt: new Date("2024-01-01T10:00:00Z"),
					metadata: { orderId: "order_001", source: "test" },
				},
				{
					stripePaymentIntentId: "pi_test_002",
					customerId: testCustomers[1].id,
					amount: 4999, // $49.99
					currency: "usd",
					status: "requires_payment_method",
					description: "Test pending payment",
					receiptEmail: testCustomers[1].email,
					metadata: { orderId: "order_002", source: "test" },
				},
				{
					stripePaymentIntentId: "pi_test_003",
					customerId: testCustomers[0].id,
					amount: 1500, // $15.00
					currency: "usd",
					status: "canceled",
					description: "Test canceled payment",
					canceledAt: new Date("2024-01-02T15:30:00Z"),
					metadata: {
						orderId: "order_003",
						source: "test",
						reason: "customer_requested",
					},
				},
				{
					stripePaymentIntentId: "pi_test_004",
					customerId: testCustomers[2].id,
					amount: 10000, // $100.00
					currency: "usd",
					status: "succeeded",
					description: "Test large payment",
					receiptEmail: testCustomers[2].email,
					succeededAt: new Date("2024-01-03T09:15:00Z"),
					metadata: { orderId: "order_004", source: "test", plan: "premium" },
				},
			])
			.returning();

		console.log(`✅ Created ${testPayments.length} test payments`);

		// Summary
		console.log("\n📊 Seeding Summary:");
		console.log(`   • Customers: ${testCustomers.length}`);
		console.log(`   • Payments: ${testPayments.length}`);
		console.log("\n🎉 Database seeding completed successfully!");

		// Display created data
		console.log("\n👥 Created Customers:");
		testCustomers.forEach((customer) => {
			console.log(
				`   • ${customer.name} (${customer.email}) - Stripe ID: ${customer.stripeCustomerId}`
			);
		});

		console.log("\n💰 Created Payments:");
		testPayments.forEach((payment) => {
			const amount = (payment.amount / 100).toFixed(2);
			console.log(
				`   • $${amount} ${payment.currency.toUpperCase()} - ${payment.status} (${payment.stripePaymentIntentId})`
			);
		});
	} catch (error) {
		console.error("❌ Seeding failed:", error);
		throw error;
	}
}

// Run seeding if this file is executed directly
if (require.main === module) {
	seed()
		.then(() => {
			console.log("✅ Seeding process completed");
		})
		.catch((error) => {
			console.error("❌ Seeding process failed:", error);
			throw error;
		});
}

export { seed };
