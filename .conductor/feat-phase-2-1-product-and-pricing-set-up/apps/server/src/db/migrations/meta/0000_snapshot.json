{"id": "c2c1a9c6-9d7f-4384-85a0-5237113d0d04", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.customers": {"name": "customers", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"customers_stripe_customer_id_unique": {"name": "customers_stripe_customer_id_unique", "nullsNotDistinct": false, "columns": ["stripe_customer_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payments": {"name": "payments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "stripe_payment_intent_id": {"name": "stripe_payment_intent_id", "type": "text", "primaryKey": false, "notNull": true}, "customer_id": {"name": "customer_id", "type": "uuid", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": true, "default": "'usd'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true}, "payment_method_id": {"name": "payment_method_id", "type": "text", "primaryKey": false, "notNull": false}, "receipt_email": {"name": "receipt_email", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "client_secret": {"name": "client_secret", "type": "text", "primaryKey": false, "notNull": false}, "canceled_at": {"name": "canceled_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "succeeded_at": {"name": "succeeded_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"payments_customer_id_customers_id_fk": {"name": "payments_customer_id_customers_id_fk", "tableFrom": "payments", "tableTo": "customers", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"payments_stripe_payment_intent_id_unique": {"name": "payments_stripe_payment_intent_id_unique", "nullsNotDistinct": false, "columns": ["stripe_payment_intent_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}