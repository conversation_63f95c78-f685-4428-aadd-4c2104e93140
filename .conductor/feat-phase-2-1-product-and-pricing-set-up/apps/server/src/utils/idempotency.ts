import { randomUUID } from "crypto";

// In-memory store for request idempotency (in production, use Redis or database)
const idempotencyStore = new Map<
	string,
	{
		response: any;
		timestamp: number;
		ttl: number;
	}
>();

// Cleanup interval for expired idempotency keys (5 minutes)
const CLEANUP_INTERVAL = 5 * 60 * 1000;
const DEFAULT_TTL = 24 * 60 * 60 * 1000; // 24 hours

// Clean up expired keys periodically
globalThis.setInterval(() => {
	const now = Date.now();
	for (const [key, value] of idempotencyStore.entries()) {
		if (now > value.timestamp + value.ttl) {
			idempotencyStore.delete(key);
		}
	}
}, CLEANUP_INTERVAL);

export interface IdempotencyOptions {
	ttl?: number; // Time to live in milliseconds
	keyPrefix?: string;
}

export class IdempotencyManager {
	private prefix: string;
	private ttl: number;

	constructor(options: IdempotencyOptions = {}) {
		this.prefix = options.keyPrefix || "idem";
		this.ttl = options.ttl || DEFAULT_TTL;
	}

	// Generate idempotency key
	generateKey(userId?: string, operation?: string): string {
		const timestamp = Date.now();
		const random = randomUUID().substring(0, 8);
		const parts = [this.prefix, timestamp, random];

		if (userId) parts.push(userId);
		if (operation) parts.push(operation);

		return parts.join("_");
	}

	// Check if request has been processed before
	checkDuplicate(key: string): any | null {
		const stored = idempotencyStore.get(key);

		if (!stored) {
			return null;
		}

		// Check if expired
		if (Date.now() > stored.timestamp + stored.ttl) {
			idempotencyStore.delete(key);
			return null;
		}

		return stored.response;
	}

	// Store response for future duplicate checks
	storeResponse(key: string, response: any): void {
		idempotencyStore.set(key, {
			response,
			timestamp: Date.now(),
			ttl: this.ttl,
		});
	}

	// Create Stripe-compatible idempotency key
	createStripeIdempotencyKey(
		customerEmail: string,
		amount: number,
		operation: string = "payment"
	): string {
		// Create deterministic key based on customer email, amount, and timestamp (rounded to minute)
		const minuteTimestamp = Math.floor(Date.now() / 60000) * 60000;
		const baseString = `${operation}_${customerEmail}_${amount}_${minuteTimestamp}`;

		// Generate hash-like string (simple approach, in production use crypto.createHash)
		let hash = 0;
		for (let i = 0; i < baseString.length; i++) {
			const char = baseString.charCodeAt(i);
			hash = (hash << 5) - hash + char;
			hash = hash & hash; // Convert to 32-bit integer
		}

		return `${operation}_${Math.abs(hash).toString(36)}`;
	}

	// Validate idempotency key format
	isValidKey(key: string): boolean {
		// Basic validation for key format
		return typeof key === "string" && key.length > 0 && key.length <= 255;
	}
}

// Default instance for payment operations
export const paymentIdempotency = new IdempotencyManager({
	keyPrefix: "payment",
	ttl: DEFAULT_TTL,
});

// Utility functions
export const generateIdempotencyKey = (
	operation: string,
	data: any
): string => {
	return paymentIdempotency.generateKey(data.customer?.email, operation);
};

export const checkIdempotencyKey = (key: string): any | null => {
	return paymentIdempotency.checkDuplicate(key);
};

export const storeIdempotencyResponse = (key: string, response: any): void => {
	paymentIdempotency.storeResponse(key, response);
};
