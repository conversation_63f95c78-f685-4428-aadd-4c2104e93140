import type { Response } from "express";
import { type ZodError } from "zod";

export class AppError extends Error {
	public statusCode: number;
	public isOperational: boolean;
	public requestId?: string;

	constructor(
		message: string,
		statusCode: number = 500,
		isOperational: boolean = true,
		requestId?: string
	) {
		super(message);
		this.statusCode = statusCode;
		this.isOperational = isOperational;
		this.requestId = requestId;

		Error.captureStackTrace(this, this.constructor);
	}
}

export class ValidationError extends AppError {
	public validationErrors: Record<string, string[]>;

	constructor(
		message: string,
		validationErrors: Record<string, string[]>,
		requestId?: string
	) {
		super(message, 400, true, requestId);
		this.validationErrors = validationErrors;
	}
}

export class StripeError extends AppError {
	public stripeCode?: string;
	public stripeType?: string;

	constructor(
		message: string,
		statusCode: number,
		stripeCode?: string,
		stripeType?: string,
		requestId?: string
	) {
		super(message, statusCode, true, requestId);
		this.stripeCode = stripeCode;
		this.stripeType = stripeType;
	}
}

// Handle Zod validation errors
export const handleZodError = (error: ZodError): ValidationError => {
	const validationErrors: Record<string, string[]> = {};

	error.issues.forEach((err: any) => {
		const path = err.path.join(".");
		if (!validationErrors[path]) {
			validationErrors[path] = [];
		}
		validationErrors[path].push(err.message);
	});

	return new ValidationError("Validation failed", validationErrors);
};

// Handle Stripe API errors
export const handleStripeError = (error: any): StripeError => {
	let statusCode = 500;
	let message = "Payment processing error";

	switch (error.type) {
		case "card_error":
			statusCode = 400;
			message = error.message || "Your card was declined";
			break;
		case "validation_error":
			statusCode = 400;
			message = error.message || "Invalid request parameters";
			break;
		case "authentication_error":
			statusCode = 401;
			message = "Authentication failed";
			break;
		case "api_connection_error":
			statusCode = 502;
			message = "Network error, please try again";
			break;
		case "api_error":
			statusCode = 502;
			message = "Payment service temporarily unavailable";
			break;
		case "rate_limit_error":
			statusCode = 429;
			message = "Too many requests, please try again later";
			break;
		case "idempotency_error":
			statusCode = 400;
			message = "Duplicate request detected";
			break;
		default:
			message = error.message || "Payment processing error";
	}

	return new StripeError(message, statusCode, error.code, error.type);
};

// Send error response
export const sendErrorResponse = (
	res: Response,
	error: Error,
	requestId: string
): void => {
	let statusCode = 500;
	let message = "Internal server error";
	let details: any = undefined;

	if (error instanceof ValidationError) {
		statusCode = error.statusCode;
		message = error.message;
		details = { validationErrors: error.validationErrors };
	} else if (error instanceof StripeError) {
		statusCode = error.statusCode;
		message = error.message;
		details = {
			stripeCode: error.stripeCode,
			stripeType: error.stripeType,
		};
	} else if (error instanceof AppError) {
		statusCode = error.statusCode;
		message = error.message;
	}

	// Log error for debugging (don't log sensitive data)
	console.error(`[${requestId}] Error:`, {
		message: error.message,
		statusCode,
		stack: error.stack,
		...(error instanceof StripeError && {
			stripeCode: error.stripeCode,
			stripeType: error.stripeType,
		}),
	});

	res.status(statusCode).json({
		error: message,
		details,
		requestId,
		timestamp: new Date().toISOString(),
	});
};

// Generate request ID for tracking
export const generateRequestId = (): string => {
	return `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
};
