import { eq } from "drizzle-orm";
import { db } from "../db";
import { payments, type Payment, insertPaymentSchema } from "../db/schema";
import { stripe, formatAmountForStripe } from "../lib/stripe";
import type {
	CreatePaymentIntentRequest,
	CreatePaymentIntentResponse,
	ConfirmPaymentRequest,
	ConfirmPaymentResponse,
	PaymentDetailsResponse,
} from "../types/api";
import { AppError, handleStripeError } from "../utils/errors";
import { CustomerService } from "./customer.service";

export class PaymentService {
	// Create payment intent
	static async createPaymentIntent(
		data: CreatePaymentIntentRequest,
		requestId: string,
		idempotencyKey?: string
	): Promise<CreatePaymentIntentResponse> {
		try {
			// Get or create customer
			const customer = await CustomerService.getOrCreateCustomer(
				data.customer,
				idempotencyKey
			);

			// Convert amount to cents
			const amountInCents = formatAmountForStripe(data.amount, data.currency);

			// Create payment intent in Stripe
			const paymentIntent = await stripe.paymentIntents.create(
				{
					amount: amountInCents,
					currency: data.currency.toLowerCase(),
					customer: customer.stripeCustomerId,
					description: data.description,
					receipt_email: data.customer.email,
					metadata: {
						customerId: customer.id,
						requestId,
						...data.metadata,
					},
					automatic_payment_methods: data.automatic_payment_methods || {
						enabled: true,
					},
				},
				idempotencyKey ? { idempotencyKey } : undefined
			);

			// Create payment record in database
			const paymentData = insertPaymentSchema.parse({
				stripePaymentIntentId: paymentIntent.id,
				customerId: customer.id,
				amount: amountInCents,
				currency: data.currency.toLowerCase(),
				status: paymentIntent.status,
				receiptEmail: data.customer.email,
				description: data.description,
				metadata: data.metadata,
				clientSecret: paymentIntent.client_secret,
			});

			const [payment] = await db
				.insert(payments)
				.values(paymentData)
				.returning();

			if (!payment) {
				throw new AppError(
					"Failed to create payment record",
					500,
					true,
					requestId
				);
			}

			if (!paymentIntent.client_secret) {
				throw new AppError(
					"Failed to get client secret from Stripe",
					500,
					true,
					requestId
				);
			}

			return {
				clientSecret: paymentIntent.client_secret,
				paymentIntentId: paymentIntent.id,
				customerId: customer.id,
				amount: amountInCents,
				currency: data.currency.toLowerCase(),
			};
		} catch (error) {
			// Handle Stripe errors
			if (error && typeof error === "object" && "type" in error) {
				throw handleStripeError(error as any);
			}

			if (error instanceof AppError) {
				throw error;
			}

			throw new AppError(
				"Failed to create payment intent",
				500,
				true,
				requestId
			);
		}
	}

	// Confirm payment and update status
	static async confirmPayment(
		data: ConfirmPaymentRequest,
		requestId: string
	): Promise<ConfirmPaymentResponse> {
		try {
			// Retrieve payment intent from Stripe
			const paymentIntent = await stripe.paymentIntents.retrieve(
				data.paymentIntentId
			);

			// Find local payment record
			const [payment] = await db
				.select()
				.from(payments)
				.where(eq(payments.stripePaymentIntentId, data.paymentIntentId))
				.limit(1);

			if (!payment) {
				throw new AppError("Payment not found", 404, true, requestId);
			}

			// Update payment status in database
			const updateData: any = {
				status: paymentIntent.status,
				paymentMethodId: (paymentIntent.payment_method as string) || null,
				updatedAt: new Date(),
			};

			// Set success/cancel timestamps
			if (paymentIntent.status === "succeeded") {
				updateData.succeededAt = new Date();
			} else if (paymentIntent.status === "canceled") {
				updateData.canceledAt = new Date();
			}

			const [updatedPayment] = await db
				.update(payments)
				.set(updateData)
				.where(eq(payments.id, payment.id))
				.returning();

			if (!updatedPayment) {
				throw new AppError(
					"Failed to update payment status",
					500,
					true,
					requestId
				);
			}

			const success = paymentIntent.status === "succeeded";

			return {
				success,
				payment: {
					id: updatedPayment.id,
					status: updatedPayment.status,
					amount: updatedPayment.amount,
					currency: updatedPayment.currency,
					customerId: updatedPayment.customerId,
				},
				error: success ? undefined : `Payment ${paymentIntent.status}`,
			};
		} catch (error) {
			// Handle Stripe errors
			if (error && typeof error === "object" && "type" in error) {
				throw handleStripeError(error as any);
			}

			if (error instanceof AppError) {
				throw error;
			}

			throw new AppError("Failed to confirm payment", 500, true, requestId);
		}
	}

	// Get payment details with customer information
	static async getPaymentDetails(
		paymentId: string,
		requestId: string
	): Promise<PaymentDetailsResponse> {
		try {
			// Get payment with customer relation
			const paymentWithCustomer = await db.query.payments.findFirst({
				where: eq(payments.id, paymentId),
				with: {
					customer: true,
				},
			});

			if (!paymentWithCustomer) {
				throw new AppError("Payment not found", 404, true, requestId);
			}

			// Optionally sync with Stripe for latest status
			// (In production, you might want to do this selectively)
			let currentStatus = paymentWithCustomer.status;
			try {
				const stripePaymentIntent = await stripe.paymentIntents.retrieve(
					paymentWithCustomer.stripePaymentIntentId
				);
				currentStatus = stripePaymentIntent.status;

				// Update local status if it differs
				if (currentStatus !== paymentWithCustomer.status) {
					await this.updatePaymentStatus(paymentId, currentStatus, requestId);
				}
			} catch (_stripeError) {
				// Log but don't fail if Stripe sync fails
				console.warn(
					`[${requestId}] Failed to sync payment status with Stripe:`,
					_stripeError
				);
			}

			return {
				id: paymentWithCustomer.id,
				stripePaymentIntentId: paymentWithCustomer.stripePaymentIntentId,
				amount: paymentWithCustomer.amount,
				currency: paymentWithCustomer.currency,
				status: currentStatus,
				description: paymentWithCustomer.description || undefined,
				receiptEmail: paymentWithCustomer.receiptEmail || undefined,
				paymentMethodId: paymentWithCustomer.paymentMethodId || undefined,
				metadata:
					(paymentWithCustomer.metadata as Record<string, string>) || undefined,
				customer: {
					id: paymentWithCustomer.customer.id,
					email: paymentWithCustomer.customer.email,
					name: paymentWithCustomer.customer.name || undefined,
					phone: paymentWithCustomer.customer.phone || undefined,
				},
				createdAt: paymentWithCustomer.createdAt.toISOString(),
				updatedAt: paymentWithCustomer.updatedAt.toISOString(),
				succeededAt: paymentWithCustomer.succeededAt?.toISOString(),
				canceledAt: paymentWithCustomer.canceledAt?.toISOString(),
			};
		} catch (error) {
			if (error instanceof AppError) {
				throw error;
			}

			throw new AppError(
				"Failed to retrieve payment details",
				500,
				true,
				requestId
			);
		}
	}

	// Update payment status
	static async updatePaymentStatus(
		paymentId: string,
		status: string,
		requestId: string
	): Promise<Payment> {
		try {
			const updateData: any = {
				status,
				updatedAt: new Date(),
			};

			// Set timestamp fields based on status
			if (status === "succeeded") {
				updateData.succeededAt = new Date();
			} else if (status === "canceled") {
				updateData.canceledAt = new Date();
			}

			const [updatedPayment] = await db
				.update(payments)
				.set(updateData)
				.where(eq(payments.id, paymentId))
				.returning();

			if (!updatedPayment) {
				throw new AppError(
					"Payment not found for status update",
					404,
					true,
					requestId
				);
			}

			return updatedPayment;
		} catch (error) {
			if (error instanceof AppError) {
				throw error;
			}

			throw new AppError(
				"Failed to update payment status",
				500,
				true,
				requestId
			);
		}
	}

	// Get customer payment history
	static async getCustomerPayments(
		customerId: string,
		requestId: string,
		options: { limit?: number; offset?: number } = {}
	): Promise<PaymentDetailsResponse[]> {
		try {
			const { limit = 10, offset = 0 } = options;

			const customerPayments = await db.query.payments.findMany({
				where: eq(payments.customerId, customerId),
				with: {
					customer: true,
				},
				limit,
				offset,
				orderBy: (payments, { desc }) => [desc(payments.createdAt)],
			});

			return customerPayments.map((payment) => ({
				id: payment.id,
				stripePaymentIntentId: payment.stripePaymentIntentId,
				amount: payment.amount,
				currency: payment.currency,
				status: payment.status,
				description: payment.description || undefined,
				receiptEmail: payment.receiptEmail || undefined,
				paymentMethodId: payment.paymentMethodId || undefined,
				metadata: (payment.metadata as Record<string, string>) || undefined,
				customer: {
					id: payment.customer.id,
					email: payment.customer.email,
					name: payment.customer.name || undefined,
					phone: payment.customer.phone || undefined,
				},
				createdAt: payment.createdAt.toISOString(),
				updatedAt: payment.updatedAt.toISOString(),
				succeededAt: payment.succeededAt?.toISOString(),
				canceledAt: payment.canceledAt?.toISOString(),
			}));
		} catch {
			throw new AppError(
				"Failed to retrieve customer payments",
				500,
				true,
				requestId
			);
		}
	}

	// Cancel payment intent
	static async cancelPayment(
		paymentId: string,
		requestId: string
	): Promise<Payment> {
		try {
			// Get payment record
			const [payment] = await db
				.select()
				.from(payments)
				.where(eq(payments.id, paymentId))
				.limit(1);

			if (!payment) {
				throw new AppError("Payment not found", 404, true, requestId);
			}

			// Cancel in Stripe if still cancelable
			if (
				[
					"requires_payment_method",
					"requires_confirmation",
					"requires_action",
				].includes(payment.status)
			) {
				await stripe.paymentIntents.cancel(payment.stripePaymentIntentId, {
					cancellation_reason: "requested_by_customer",
				});
			}

			// Update local record
			return this.updatePaymentStatus(paymentId, "canceled", requestId);
		} catch (error) {
			if (error && typeof error === "object" && "type" in error) {
				throw handleStripeError(error as any);
			}

			if (error instanceof AppError) {
				throw error;
			}

			throw new AppError("Failed to cancel payment", 500, true, requestId);
		}
	}
}
