import { eq } from "drizzle-orm";
import { db } from "../db";
import { customers, type Customer, insertCustomerSchema } from "../db/schema";
import { stripe } from "../lib/stripe";
import type { CreatePaymentIntentRequest } from "../types/api";
import { AppError } from "../utils/errors";

export class CustomerService {
	// Find customer by email
	static async findByEmail(email: string): Promise<Customer | null> {
		try {
			const [customer] = await db
				.select()
				.from(customers)
				.where(eq(customers.email, email))
				.limit(1);

			return customer || null;
		} catch {
			throw new AppError("Failed to lookup customer", 500);
		}
	}

	// Find customer by Stripe customer ID
	static async findByStripeId(
		stripeCustomerId: string
	): Promise<Customer | null> {
		try {
			const [customer] = await db
				.select()
				.from(customers)
				.where(eq(customers.stripeCustomerId, stripeCustomerId))
				.limit(1);

			return customer || null;
		} catch {
			throw new AppError("Failed to lookup customer by Stripe ID", 500);
		}
	}

	// Create customer in both Stripe and local database
	static async createCustomer(
		customerData: CreatePaymentIntentRequest["customer"],
		idempotencyKey?: string
	): Promise<Customer> {
		try {
			// Create customer in Stripe first
			const stripeCustomer = await stripe.customers.create(
				{
					email: customerData.email,
					name: customerData.name,
					phone: customerData.phone,
					metadata: {
						source: "payment_app",
						created_via: "create_payment_intent",
					},
				},
				idempotencyKey
					? { idempotencyKey: `customer_${idempotencyKey}` }
					: undefined
			);

			// Validate customer data
			const validatedData = insertCustomerSchema.parse({
				stripeCustomerId: stripeCustomer.id,
				email: customerData.email,
				name: customerData.name || null,
				phone: customerData.phone || null,
			});

			// Create customer in local database
			const [newCustomer] = await db
				.insert(customers)
				.values(validatedData)
				.returning();

			if (!newCustomer) {
				throw new AppError("Failed to create customer record", 500);
			}

			return newCustomer;
		} catch (error) {
			// If it's a Stripe error, handle it appropriately
			if (error instanceof Error && "type" in error) {
				const stripeError = error as any;
				if (stripeError.type === "StripeCardError") {
					throw new AppError("Invalid customer data", 400);
				}
			}

			if (error instanceof AppError) {
				throw error;
			}

			throw new AppError("Failed to create customer", 500);
		}
	}

	// Get or create customer (main function for payment intent creation)
	static async getOrCreateCustomer(
		customerData: CreatePaymentIntentRequest["customer"],
		idempotencyKey?: string
	): Promise<Customer> {
		// First, try to find existing customer
		const existingCustomer = await this.findByEmail(customerData.email);

		if (existingCustomer) {
			// Update customer info if needed (name or phone changed)
			if (
				(customerData.name && customerData.name !== existingCustomer.name) ||
				(customerData.phone && customerData.phone !== existingCustomer.phone)
			) {
				await this.updateCustomer(existingCustomer.id, {
					name: customerData.name,
					phone: customerData.phone,
				});

				// Return updated customer data
				return {
					...existingCustomer,
					name: customerData.name || existingCustomer.name,
					phone: customerData.phone || existingCustomer.phone,
					updatedAt: new Date(),
				};
			}

			return existingCustomer;
		}

		// Create new customer if not found
		return this.createCustomer(customerData, idempotencyKey);
	}

	// Update customer information
	static async updateCustomer(
		customerId: string,
		updateData: Partial<Pick<Customer, "name" | "phone">>
	): Promise<Customer> {
		try {
			const [updatedCustomer] = await db
				.update(customers)
				.set({
					...updateData,
					updatedAt: new Date(),
				})
				.where(eq(customers.id, customerId))
				.returning();

			if (!updatedCustomer) {
				throw new AppError("Customer not found", 404);
			}

			return updatedCustomer;
		} catch (error) {
			if (error instanceof AppError) {
				throw error;
			}

			throw new AppError("Failed to update customer", 500);
		}
	}

	// Sync customer data with Stripe
	static async syncWithStripe(customerId: string): Promise<Customer> {
		try {
			const customer = await db.query.customers.findFirst({
				where: eq(customers.id, customerId),
			});

			if (!customer) {
				throw new AppError("Customer not found", 404);
			}

			// Get latest data from Stripe
			const stripeCustomer = await stripe.customers.retrieve(
				customer.stripeCustomerId
			);

			if (stripeCustomer.deleted) {
				throw new AppError("Customer has been deleted in Stripe", 404);
			}

			// Update local record if needed
			const needsUpdate =
				stripeCustomer.email !== customer.email ||
				stripeCustomer.name !== customer.name ||
				stripeCustomer.phone !== customer.phone;

			if (needsUpdate) {
				return this.updateCustomer(customer.id, {
					name: stripeCustomer.name || undefined,
					phone: stripeCustomer.phone || undefined,
				});
			}

			return customer;
		} catch (error) {
			if (error instanceof AppError) {
				throw error;
			}

			throw new AppError("Failed to sync customer with Stripe", 500);
		}
	}
}
