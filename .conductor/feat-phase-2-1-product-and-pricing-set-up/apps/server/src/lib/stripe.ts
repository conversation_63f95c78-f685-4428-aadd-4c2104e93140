import Stripe from "stripe";

if (!process.env.STRIPE_SECRET_KEY) {
	throw new Error("STRIPE_SECRET_KEY is not defined in environment variables");
}

if (!process.env.STRIPE_PUBLISHABLE_KEY) {
	throw new Error(
		"STRIPE_PUBLISHABLE_KEY is not defined in environment variables"
	);
}

// Initialize Stripe with the secret key
export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
	apiVersion: "2025-08-27.basil", // Use the latest API version
	typescript: true,
});

// Export the publishable key for client-side usage
export const stripePublishableKey = process.env.STRIPE_PUBLISHABLE_KEY;

// Export webhook secret for webhook verification
export const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

// Utility function to format currency amounts
export const formatAmountForStripe = (amount: number, currency: string) => {
	// Convert to smallest currency unit (cents for USD)
	const numberFormat = new Intl.NumberFormat(["en-US"], {
		style: "currency",
		currency: currency,
		currencyDisplay: "symbol",
	});
	const parts = numberFormat.formatToParts(amount);
	let zeroDecimalCurrency = true;
	for (const part of parts) {
		if (part.type === "decimal") {
			zeroDecimalCurrency = false;
		}
	}
	return zeroDecimalCurrency ? amount : Math.round(amount * 100);
};

// Utility function to format amount from Stripe (cents to dollars)
export const formatAmountFromStripe = (amount: number, currency: string) => {
	const numberFormat = new Intl.NumberFormat(["en-US"], {
		style: "currency",
		currency: currency,
		currencyDisplay: "symbol",
	});
	const parts = numberFormat.formatToParts(amount / 100);
	let zeroDecimalCurrency = true;
	for (const part of parts) {
		if (part.type === "decimal") {
			zeroDecimalCurrency = false;
		}
	}
	return zeroDecimalCurrency ? amount : amount / 100;
};
