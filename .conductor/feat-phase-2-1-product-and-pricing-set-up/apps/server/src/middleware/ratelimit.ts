import type { Request, Response } from "express";
import rateLimit from "express-rate-limit";
import slowDown from "express-slow-down";

// Rate limiter for payment endpoints
export const paymentRateLimit = rateLimit({
	windowMs: 15 * 60 * 1000, // 15 minutes
	max: 10, // Limit each IP to 10 payment requests per windowMs
	message: {
		error: "Too many payment attempts from this IP, please try again later.",
		retryAfter: "15 minutes",
	},
	standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
	legacyHeaders: false, // Disable the `X-RateLimit-*` headers
	handler: (req: Request, res: Response) => {
		res.status(429).json({
			error: "Too many payment attempts from this IP, please try again later.",
			retryAfter: "15 minutes",
		});
	},
});

// Speed limiter for payment endpoints (progressively slow down requests)
export const paymentSpeedLimit = slowDown({
	windowMs: 15 * 60 * 1000, // 15 minutes
	delayAfter: 3, // Allow 3 requests per windowMs without delay
	delayMs: (hits) => hits * 500, // Add 500ms delay per request after delayAfter
	maxDelayMs: 5000, // Maximum delay of 5 seconds
});

// General API rate limiter
export const generalRateLimit = rateLimit({
	windowMs: 15 * 60 * 1000, // 15 minutes
	max: 100, // Limit each IP to 100 requests per windowMs
	message: {
		error: "Too many requests from this IP, please try again later.",
		retryAfter: "15 minutes",
	},
	standardHeaders: true,
	legacyHeaders: false,
});

// Webhook rate limiter (more lenient as it's from Stripe)
export const webhookRateLimit = rateLimit({
	windowMs: 15 * 60 * 1000, // 15 minutes
	max: 1000, // Allow more webhook requests
	message: {
		error: "Too many webhook requests",
	},
	standardHeaders: true,
	legacyHeaders: false,
	skip: (req) => {
		// Skip rate limiting for requests from Stripe IPs (basic check)
		const stripeIPs = [
			"**************",
			"**************",
			"*************",
			// Add more Stripe webhook IPs as needed
		];
		return stripeIPs.includes(req.ip || "");
	},
});
