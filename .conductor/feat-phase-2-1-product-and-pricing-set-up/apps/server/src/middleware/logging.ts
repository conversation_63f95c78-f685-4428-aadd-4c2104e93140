import type { Request, Response, NextFunction } from "express";

export interface LogData {
	method: string;
	url: string;
	statusCode?: number;
	responseTime?: number;
	ip: string;
	userAgent: string;
	timestamp: string;
	requestId: string;
	body?: any;
	error?: string;
}

// Generate a unique request ID
const generateRequestId = () => {
	return Math.random().toString(36).substr(2, 9);
};

// Sanitize request body to remove sensitive data
const sanitizeBody = (body: any): any => {
	if (!body || typeof body !== "object") {
		return body;
	}

	const sensitiveFields = [
		"password",
		"token",
		"secret",
		"key",
		"card",
		"number",
		"cvc",
		"cvv",
		"exp_month",
		"exp_year",
		"client_secret",
	];

	const sanitized = { ...body };

	const sanitizeObject = (obj: any): any => {
		if (Array.isArray(obj)) {
			return obj.map(sanitizeObject);
		}

		if (obj && typeof obj === "object") {
			const result: any = {};
			for (const [key, value] of Object.entries(obj)) {
				const lowerKey = key.toLowerCase();
				const isSensitive = sensitiveFields.some((field) =>
					lowerKey.includes(field)
				);

				if (isSensitive) {
					result[key] = "[REDACTED]";
				} else if (typeof value === "object" && value !== null) {
					result[key] = sanitizeObject(value);
				} else {
					result[key] = value;
				}
			}
			return result;
		}

		return obj;
	};

	return sanitizeObject(sanitized);
};

// Request logging middleware
export const requestLogger = (
	req: Request,
	res: Response,
	next: NextFunction
) => {
	const startTime = Date.now();
	const requestId = generateRequestId();

	// Add request ID to request object for downstream use
	(req as any).requestId = requestId;

	const logData: LogData = {
		method: req.method,
		url: req.url,
		ip: req.ip || req.connection.remoteAddress || "unknown",
		userAgent: req.get("User-Agent") || "unknown",
		timestamp: new Date().toISOString(),
		requestId,
	};

	// Log request start (only for payment endpoints to avoid spam)
	if (req.url.includes("/api/payments") || req.url.includes("/api/webhooks")) {
		console.log(
			`[${logData.requestId}] ${logData.method} ${logData.url} - Request started`,
			{
				...logData,
				body: req.method !== "GET" ? sanitizeBody(req.body) : undefined,
			}
		);
	}

	// Override res.json to log response
	const originalJson = res.json;
	res.json = function (body: any) {
		const responseTime = Date.now() - startTime;

		const finalLogData: LogData = {
			...logData,
			statusCode: res.statusCode,
			responseTime,
		};

		// Log response (only for payment endpoints and errors)
		if (
			req.url.includes("/api/payments") ||
			req.url.includes("/api/webhooks") ||
			res.statusCode >= 400
		) {
			const logLevel = res.statusCode >= 400 ? "ERROR" : "INFO";
			console.log(
				`[${logData.requestId}] ${logData.method} ${logData.url} - ${logLevel}`,
				{
					...finalLogData,
					error: res.statusCode >= 400 ? body : undefined,
				}
			);
		}

		return originalJson.call(this, body);
	};

	// Handle errors
	res.on("error", (error: Error) => {
		console.error(
			`[${logData.requestId}] ${logData.method} ${logData.url} - ERROR`,
			{
				...logData,
				statusCode: res.statusCode,
				responseTime: Date.now() - startTime,
				error: error.message,
			}
		);
	});

	next();
};

// Error logging middleware
export const errorLogger = (error: Error, req: Request, res: Response) => {
	const requestId = (req as any).requestId || "unknown";

	console.error(`[${requestId}] ${req.method} ${req.url} - UNHANDLED ERROR`, {
		method: req.method,
		url: req.url,
		ip: req.ip || "unknown",
		timestamp: new Date().toISOString(),
		requestId,
		error: {
			name: error.name,
			message: error.message,
			stack: error.stack,
		},
		body: req.method !== "GET" ? sanitizeBody(req.body) : undefined,
	});

	// Don't expose internal error details in production
	const isDevelopment = process.env.NODE_ENV === "development";

	res.status(500).json({
		error: "Internal Server Error",
		message: isDevelopment ? error.message : "Something went wrong",
		requestId,
		...(isDevelopment && { stack: error.stack }),
	});
};
