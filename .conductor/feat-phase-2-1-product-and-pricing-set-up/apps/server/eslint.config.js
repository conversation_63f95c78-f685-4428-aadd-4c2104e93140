import baseConfig from "../../eslint.config.js";

export default [
	...baseConfig,
	{
		files: ["**/*.{js,ts}"],
		languageOptions: {
			parserOptions: {
				project: "./tsconfig.json",
			},
			globals: {
				process: "readonly",
				Buffer: "readonly",
				__dirname: "readonly",
				__filename: "readonly",
				global: "readonly",
			},
		},
		rules: {
			// Express/API specific rules
			"no-process-exit": "error",

			// Import rules for server
			"import/order": [
				"error",
				{
					groups: [
						"builtin",
						"external",
						"internal",
						"parent",
						"sibling",
						"index",
					],
					"newlines-between": "never",
					alphabetize: {
						order: "asc",
						caseInsensitive: true,
					},
					pathGroups: [
						{
							pattern: "@/**",
							group: "internal",
						},
					],
				},
			],

			// Console rules (allow console in server environment)
			"no-console": "off",

			// Security rules
			"no-eval": "error",
			"no-implied-eval": "error",
			"no-new-func": "error",
		},
	},
];
