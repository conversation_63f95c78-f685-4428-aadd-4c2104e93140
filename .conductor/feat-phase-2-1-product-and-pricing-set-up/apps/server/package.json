{"name": "server", "main": "src/index.ts", "type": "module", "scripts": {"build": "tsdown", "check-types": "tsc -b", "compile": "bun build --compile --minify --sourcemap --bytecode ./src/index.ts --outfile server", "dev": "bun run --hot src/index.ts", "start": "bun run dist/index.js", "lint": "eslint . --ext ts --report-unused-disable-directives --max-warnings 15", "lint:fix": "eslint . --ext ts --fix", "format": "prettier --write .", "format:check": "prettier --check .", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:seed": "bun run src/db/seed.ts"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^17.2.1", "drizzle-orm": "^0.44.2", "drizzle-zod": "^0.8.3", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "express-slow-down": "^3.0.0", "pg": "^8.14.1", "stripe": "^18.5.0", "zod": "^4.0.2"}, "trustedDependencies": ["supabase"], "devDependencies": {"@types/bun": "^1.2.6", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/express-serve-static-core": "^5.0.7", "@types/node": "^24.3.0", "@types/pg": "^8.11.11", "drizzle-kit": "^0.31.2", "eslint-plugin-node": "^11.1.0", "tsdown": "^0.14.1", "typescript": "^5.8.2"}}