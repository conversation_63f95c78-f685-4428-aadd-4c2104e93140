import { useState, useEffect, useCallback } from "react";
import { paymentApi, type PaymentDetails } from "@/lib/api";

interface UsePaymentStatusOptions {
	pollingInterval?: number; // in milliseconds
	maxPollingTime?: number; // in milliseconds
	enablePolling?: boolean;
}

export function usePaymentStatus(
	paymentId: string | null,
	options: UsePaymentStatusOptions = {}
) {
	const {
		pollingInterval = 3000, // 3 seconds
		maxPollingTime = 300000, // 5 minutes
		enablePolling = true,
	} = options;

	const [paymentDetails, setPaymentDetails] = useState<PaymentDetails | null>(
		null
	);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [isPolling, setIsPolling] = useState(false);
	const [pollCount, setPollCount] = useState(0);

	const fetchPaymentStatus = useCallback(async () => {
		if (!paymentId) return;

		setLoading(true);
		setError(null);

		try {
			const details = await paymentApi.getPayment(paymentId);
			setPaymentDetails(details);
			return details;
		} catch (err) {
			const errorMessage =
				err instanceof Error ? err.message : "Failed to fetch payment status";
			setError(errorMessage);
			throw err;
		} finally {
			setLoading(false);
		}
	}, [paymentId]);

	// Start polling for payment status updates
	const startPolling = () => {
		if (!paymentId || !enablePolling || isPolling) return;

		setIsPolling(true);
		setPollCount(0);

		const startTime = Date.now();

		const poll = async () => {
			try {
				const details = await fetchPaymentStatus();
				setPollCount((prev) => prev + 1);

				// Stop polling if payment is in a final state
				if (
					(details && details.status === "succeeded") ||
					(details && details.status === "canceled") ||
					(details && details.status === "failed")
				) {
					stopPolling();
					return;
				}

				// Stop polling if max time reached
				if (Date.now() - startTime > maxPollingTime) {
					stopPolling();
					setError("Payment status check timed out");
					return;
				}

				// Continue polling
				window.setTimeout(poll, pollingInterval);
			} catch {
				// Stop polling on error
				stopPolling();
			}
		};

		poll();
	};

	const stopPolling = () => {
		setIsPolling(false);
	};

	// Manual refresh
	const refresh = () => {
		fetchPaymentStatus();
	};

	// Auto-fetch on paymentId change
	useEffect(() => {
		if (paymentId) {
			fetchPaymentStatus();
		} else {
			setPaymentDetails(null);
			setError(null);
		}
	}, [paymentId, fetchPaymentStatus]);

	// Cleanup polling on unmount
	useEffect(() => {
		return () => {
			setIsPolling(false);
		};
	}, []);

	return {
		paymentDetails,
		loading,
		error,
		isPolling,
		pollCount,
		fetchPaymentStatus: fetchPaymentStatus,
		startPolling,
		stopPolling,
		refresh,
	};
}

// Hook for checking multiple payments (e.g., customer payment history)
export function usePaymentHistory(
	customerId: string | null,
	options?: { limit?: number }
) {
	const [payments, setPayments] = useState<PaymentDetails[]>([]);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const fetchPaymentHistory = useCallback(async () => {
		if (!customerId) return;

		setLoading(true);
		setError(null);

		try {
			const history = await paymentApi.getPaymentHistory(customerId, options);
			setPayments(history);
		} catch (err) {
			const errorMessage =
				err instanceof Error ? err.message : "Failed to fetch payment history";
			setError(errorMessage);
		} finally {
			setLoading(false);
		}
	}, [customerId, options]);

	useEffect(() => {
		if (customerId) {
			fetchPaymentHistory();
		} else {
			setPayments([]);
			setError(null);
		}
	}, [customerId, options?.limit, fetchPaymentHistory]);

	return {
		payments,
		loading,
		error,
		refresh: fetchPaymentHistory,
	};
}

// Hook for real-time payment status with WebSocket fallback to polling
export function useRealTimePaymentStatus(paymentId: string | null) {
	const [connectionStatus, setConnectionStatus] = useState<
		"disconnected" | "connecting" | "connected"
	>("disconnected");
	const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

	const {
		paymentDetails,
		loading,
		error,
		isPolling,
		startPolling,
		stopPolling,
		refresh,
	} = usePaymentStatus(paymentId, {
		pollingInterval: 2000,
		enablePolling: false, // We'll control polling manually
	});

	useEffect(() => {
		if (!paymentId) return;

		// In a real implementation, you would set up WebSocket connection here
		// For now, we'll fall back to polling
		const setupWebSocket = () => {
			setConnectionStatus("connecting");

			// Simulate WebSocket connection
			window.setTimeout(() => {
				setConnectionStatus("connected");
				setLastUpdate(new Date());
				startPolling(); // Fallback to polling
			}, 1000);
		};

		setupWebSocket();

		return () => {
			stopPolling();
			setConnectionStatus("disconnected");
		};
	}, [paymentId, startPolling, stopPolling]);

	return {
		paymentDetails,
		loading,
		error,
		connectionStatus,
		isPolling,
		lastUpdate,
		refresh,
	};
}
