"use client";

import { createContext, useContext, useEffect, useState } from "react";
import { Elements } from "@stripe/react-stripe-js";
import { loadStripe, type Stripe } from "@stripe/stripe-js";

// Initialize Stripe
const getStripePromise = () => {
	const publishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;
	if (!publishableKey) {
		throw new Error("NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY is required");
	}
	return loadStripe(publishableKey);
};

const stripePromise = getStripePromise();

interface StripeContextType {
	stripe: Stripe | null;
	isLoading: boolean;
	error: string | null;
}

const StripeContext = createContext<StripeContextType>({
	stripe: null,
	isLoading: true,
	error: null,
});

export const useStripe = () => {
	const context = useContext(StripeContext);
	if (!context) {
		throw new Error("useStripe must be used within a StripeProvider");
	}
	return context;
};

interface StripeProviderProps {
	children: React.ReactNode;
	options?: {
		appearance?: {
			theme?: "stripe" | "night" | "flat";
			variables?: Record<string, string>;
		};
		fonts?: Array<{
			cssSrc: string;
		}>;
		loader?: "auto" | "always" | "never";
	};
}

export function StripeProvider({ children, options }: StripeProviderProps) {
	const [stripe, setStripe] = useState<Stripe | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		if (!process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY) {
			setError("Stripe publishable key is not configured");
			setIsLoading(false);
			return;
		}

		stripePromise
			.then((stripeInstance) => {
				setStripe(stripeInstance);
				setIsLoading(false);
			})
			.catch((err) => {
				setError("Failed to load Stripe");
				setIsLoading(false);
				console.error("Stripe loading error:", err);
			});
	}, []);

	const defaultOptions = {
		appearance: {
			theme: "stripe" as const,
			variables: {
				colorPrimary: "hsl(var(--primary))",
				colorBackground: "hsl(var(--background))",
				colorText: "hsl(var(--foreground))",
				colorDanger: "hsl(var(--destructive))",
				fontFamily: "var(--font-sans)",
				spacingUnit: "4px",
				borderRadius: "6px",
			},
		},
		...options,
	};

	if (error) {
		return (
			<div className="flex items-center justify-center p-4 text-destructive">
				<p>Stripe configuration error: {error}</p>
			</div>
		);
	}

	if (isLoading) {
		return (
			<div className="flex items-center justify-center p-4">
				<p>Loading payment system...</p>
			</div>
		);
	}

	return (
		<StripeContext.Provider value={{ stripe, isLoading, error }}>
			<Elements stripe={stripePromise} options={defaultOptions}>
				{children}
			</Elements>
		</StripeContext.Provider>
	);
}

// Higher-order component for wrapping pages that need Stripe
export function withStripe<P extends object>(
	Component: React.ComponentType<P>
) {
	return function WrappedComponent(props: P) {
		return (
			<StripeProvider>
				<Component {...props} />
			</StripeProvider>
		);
	};
}
