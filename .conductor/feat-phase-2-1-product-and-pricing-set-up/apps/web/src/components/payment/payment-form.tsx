"use client";

import { useState } from "react";
import {
	useStripe,
	useElements,
	CardElement,
	Elements,
} from "@stripe/react-stripe-js";
import { Loader2, CreditCard, DollarSign, User, Mail } from "lucide-react";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { paymentApi, type CreatePaymentIntentRequest } from "@/lib/api";
import {
	getStripe,
	formatAmountForDisplay,
	formatAmountForStripe,
} from "@/lib/stripe";

interface PaymentFormProps {
	amount?: number;
	currency?: string;
	// eslint-disable-next-line no-unused-vars
	onSuccess?: (_paymentIntentId: string) => void;
	// eslint-disable-next-line no-unused-vars
	onError?: (_error: string) => void;
}

const stripePromise = getStripe();

function PaymentFormContent({
	amount: defaultAmount = 1000, // $10.00 in cents
	currency = "usd",
	onSuccess,
	onError,
}: PaymentFormProps) {
	const stripe = useStripe();
	const elements = useElements();
	const [amount, setAmount] = useState<number>(defaultAmount);
	const [customerEmail, setCustomerEmail] = useState("");
	const [customerName, setCustomerName] = useState("");
	const [isProcessing, setIsProcessing] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const handleAmountChange = (value: string) => {
		// Remove non-numeric characters and convert to cents
		const numericValue = value.replace(/[^\d.]/g, "");
		if (numericValue === "") {
			setAmount(0);
			return;
		}

		const dollars = parseFloat(numericValue);
		if (!isNaN(dollars)) {
			setAmount(formatAmountForStripe(dollars, currency));
		}
	};

	const formatAmountForInput = (cents: number): string => {
		return (cents / 100).toFixed(2);
	};

	const validateForm = (): boolean => {
		if (!stripe || !elements) {
			setError("Stripe has not loaded properly");
			return false;
		}

		if (amount < 50) {
			// $0.50 minimum
			setError("Minimum amount is $0.50");
			return false;
		}

		if (amount > 999999) {
			// $9,999.99 maximum
			setError("Maximum amount is $9,999.99");
			return false;
		}

		if (!customerEmail.trim()) {
			setError("Email is required");
			return false;
		}

		if (!isValidEmail(customerEmail)) {
			setError("Please enter a valid email address");
			return false;
		}

		setError(null);
		return true;
	};

	const isValidEmail = (email: string): boolean => {
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		return emailRegex.test(email);
	};

	const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
		event.preventDefault();

		if (!validateForm()) {
			return;
		}

		if (!elements) {
			setError("Card element not found");
			return;
		}
		if (!stripe) {
			setError("Stripe not loaded");
			return;
		}
		setIsProcessing(true);
		setError(null);

		try {
			// Create payment intent
			const paymentIntentData: CreatePaymentIntentRequest = {
				amount,
				currency,
				customer: {
					email: customerEmail.trim(),
					name: customerName.trim() || undefined,
				},
				automatic_payment_methods: {
					enabled: true,
				},
			};

			const { clientSecret, paymentIntentId } =
				await paymentApi.createPaymentIntent(paymentIntentData);

			// Confirm the payment using the CardElement
			const cardElement = elements.getElement(CardElement);
			if (!cardElement) {
				throw new Error("Card element not found");
			}

			const { error: confirmError } = await stripe.confirmCardPayment(
				clientSecret,
				{
					payment_method: {
						card: cardElement,
						billing_details: {
							email: customerEmail.trim(),
							name: customerName.trim() || undefined,
						},
					},
				}
			);

			if (confirmError) {
				throw new Error(confirmError.message || "Payment confirmation failed");
			}

			// Success!
			toast.success("Payment processed successfully!");
			onSuccess?.(paymentIntentId);

			// Reset form
			setCustomerEmail("");
			setCustomerName("");
			setAmount(defaultAmount);
		} catch (err) {
			const errorMessage =
				err instanceof Error ? err.message : "Payment failed";
			setError(errorMessage);
			toast.error(errorMessage);
			onError?.(errorMessage);
		} finally {
			setIsProcessing(false);
		}
	};

	return (
		<Card className="w-full max-w-lg mx-auto">
			<CardHeader>
				<CardTitle className="flex items-center space-x-2">
					<CreditCard className="h-5 w-5" />
					<span>Payment Details</span>
				</CardTitle>
				<CardDescription>
					Enter your payment information to complete the transaction
				</CardDescription>
			</CardHeader>
			<CardContent>
				<form onSubmit={handleSubmit} className="space-y-4">
					{/* Amount Input */}
					<div className="space-y-2">
						<Label htmlFor="amount" className="flex items-center space-x-2">
							<DollarSign className="h-4 w-4" />
							<span>Amount</span>
						</Label>
						<div className="relative">
							<span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
								$
							</span>
							<Input
								id="amount"
								type="text"
								value={formatAmountForInput(amount)}
								onChange={(e) => handleAmountChange(e.target.value)}
								placeholder="0.00"
								className="pl-8"
								disabled={isProcessing}
							/>
						</div>
						<p className="text-sm text-muted-foreground">
							{formatAmountForDisplay(amount, currency)}
						</p>
					</div>

					{/* Customer Information */}
					<div className="space-y-2">
						<Label htmlFor="email" className="flex items-center space-x-2">
							<Mail className="h-4 w-4" />
							<span>Email *</span>
						</Label>
						<Input
							id="email"
							type="email"
							value={customerEmail}
							onChange={(e) => setCustomerEmail(e.target.value)}
							placeholder="<EMAIL>"
							disabled={isProcessing}
							required
						/>
					</div>

					<div className="space-y-2">
						<Label htmlFor="name" className="flex items-center space-x-2">
							<User className="h-4 w-4" />
							<span>Name (Optional)</span>
						</Label>
						<Input
							id="name"
							type="text"
							value={customerName}
							onChange={(e) => setCustomerName(e.target.value)}
							placeholder="John Doe"
							disabled={isProcessing}
						/>
					</div>

					{/* Card Element */}
					<div className="space-y-2">
						<Label className="flex items-center space-x-2">
							<CreditCard className="h-4 w-4" />
							<span>Card Information *</span>
						</Label>
						<div className="p-3 border rounded-md">
							<CardElement
								options={{
									style: {
										base: {
											fontSize: "16px",
											color: "#424770",
											"::placeholder": {
												color: "#aab7c4",
											},
										},
										invalid: {
											color: "#9e2146",
										},
									},
								}}
							/>
						</div>
					</div>

					{/* Error Display */}
					{error && (
						<div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
							{error}
						</div>
					)}

					{/* Submit Button */}
					<Button
						type="submit"
						className="w-full"
						disabled={isProcessing || !stripe || !elements}
					>
						{isProcessing ? (
							<>
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />
								Processing...
							</>
						) : (
							<>
								<CreditCard className="mr-2 h-4 w-4" />
								Pay {formatAmountForDisplay(amount, currency)}
							</>
						)}
					</Button>
				</form>
			</CardContent>
		</Card>
	);
}

export function PaymentForm(props: PaymentFormProps) {
	return (
		<Elements stripe={stripePromise}>
			<PaymentFormContent {...props} />
		</Elements>
	);
}
