"use client";

import { CardElement, useStripe, useElements } from "@stripe/react-stripe-js";
import { useTheme } from "next-themes";
import { cn } from "@/lib/utils";

interface StripeCardElementProps {
	className?: string;
}

export function StripeCardElement({ className }: StripeCardElementProps) {
	const stripe = useStripe();
	const elements = useElements();
	const { theme } = useTheme();

	if (!stripe || !elements) {
		return (
			<div className="h-12 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background">
				<div className="flex items-center space-x-2">
					<div className="h-4 w-4 animate-pulse rounded bg-muted"></div>
					<div className="h-4 w-32 animate-pulse rounded bg-muted"></div>
				</div>
			</div>
		);
	}

	return (
		<div className={cn("space-y-2", className)}>
			<CardElement
				options={{
					style: {
						base: {
							fontSize: "16px",
							color: theme === "dark" ? "#ffffff" : "#424770",
							"::placeholder": {
								color: theme === "dark" ? "#aab7c4" : "#aab7c4",
							},
						},
						invalid: {
							color: "#9e2146",
						},
					},
				}}
			/>
		</div>
	);
}
