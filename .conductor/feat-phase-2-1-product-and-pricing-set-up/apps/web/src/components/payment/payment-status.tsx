"use client";

import {
	Check<PERSON>ir<PERSON>,
	XCircle,
	Clock,
	RefreshCw,
	Activity,
	AlertCircle,
} from "lucide-react";
import { useRealTimePaymentStatus } from "@/components/hooks/use-payment-status";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

interface PaymentStatusProps {
	paymentId: string | null;
	showDetails?: boolean;
}

export function PaymentStatus({
	paymentId,
	showDetails = true,
}: PaymentStatusProps) {
	const {
		paymentDetails,
		loading,
		error,
		connectionStatus,
		isPolling,
		lastUpdate,
		refresh,
	} = useRealTimePaymentStatus(paymentId);

	const getStatusIcon = (status: string) => {
		switch (status) {
			case "succeeded":
				return <CheckCircle className="h-4 w-4 text-green-600" />;
			case "failed":
			case "canceled":
				return <XCircle className="h-4 w-4 text-red-600" />;
			case "processing":
			case "requires_confirmation":
			case "requires_action":
				return <Clock className="h-4 w-4 text-yellow-600" />;
			default:
				return <AlertCircle className="h-4 w-4 text-gray-600" />;
		}
	};

	const getStatusColor = (status: string) => {
		switch (status) {
			case "succeeded":
				return "bg-green-100 text-green-800 border-green-200";
			case "failed":
			case "canceled":
				return "bg-red-100 text-red-800 border-red-200";
			case "processing":
			case "requires_confirmation":
			case "requires_action":
				return "bg-yellow-100 text-yellow-800 border-yellow-200";
			default:
				return "bg-gray-100 text-gray-800 border-gray-200";
		}
	};

	const formatAmount = (amount: number, currency: string): string => {
		return new Intl.NumberFormat("en-US", {
			style: "currency",
			currency: currency.toUpperCase(),
		}).format(amount / 100);
	};

	const formatDate = (dateString: string): string => {
		return new Date(dateString).toLocaleString();
	};

	if (loading && !paymentDetails) {
		return (
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center space-x-2">
						<Activity className="h-5 w-5" />
						<span>Payment Status</span>
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="space-y-4">
						<Skeleton className="h-6 w-32" />
						<Skeleton className="h-4 w-48" />
						<Skeleton className="h-4 w-64" />
					</div>
				</CardContent>
			</Card>
		);
	}

	if (error && !paymentDetails) {
		return (
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center space-x-2">
						<XCircle className="h-5 w-5 text-red-600" />
						<span>Payment Status Error</span>
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="space-y-4">
						<p className="text-sm text-red-600">{error}</p>
						<Button onClick={refresh} variant="outline" size="sm">
							<RefreshCw className="mr-2 h-4 w-4" />
							Retry
						</Button>
					</div>
				</CardContent>
			</Card>
		);
	}

	if (!paymentDetails) {
		return (
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center space-x-2">
						<Activity className="h-5 w-5" />
						<span>Payment Status</span>
					</CardTitle>
				</CardHeader>
				<CardContent>
					<p className="text-sm text-muted-foreground">
						No payment information available
					</p>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card>
			<CardHeader>
				<div className="flex items-center justify-between">
					<CardTitle className="flex items-center space-x-2">
						<Activity className="h-5 w-5" />
						<span>Payment Status</span>
					</CardTitle>
					<div className="flex items-center space-x-2">
						{isPolling && (
							<div className="flex items-center space-x-1 text-xs text-muted-foreground">
								<div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
								<span>Live</span>
							</div>
						)}
						<Button onClick={refresh} variant="ghost" size="sm">
							<RefreshCw className="h-4 w-4" />
						</Button>
					</div>
				</div>
				{lastUpdate && (
					<CardDescription>
						Last updated: {formatDate(lastUpdate.toISOString())}
					</CardDescription>
				)}
			</CardHeader>
			<CardContent>
				<div className="space-y-4">
					{/* Status Badge */}
					<div className="flex items-center space-x-3">
						{getStatusIcon(paymentDetails.status)}
						<Badge className={getStatusColor(paymentDetails.status)}>
							{paymentDetails.status.replace("_", " ").toUpperCase()}
						</Badge>
					</div>

					{showDetails && (
						<div className="space-y-3 border-t pt-4">
							{/* Amount */}
							<div className="flex justify-between items-center">
								<span className="text-sm font-medium text-muted-foreground">
									Amount
								</span>
								<span className="font-semibold">
									{formatAmount(paymentDetails.amount, paymentDetails.currency)}
								</span>
							</div>

							{/* Payment ID */}
							<div className="flex justify-between items-center">
								<span className="text-sm font-medium text-muted-foreground">
									Payment ID
								</span>
								<span className="font-mono text-sm">{paymentDetails.id}</span>
							</div>

							{/* Customer Info */}
							{paymentDetails.customer && (
								<div className="flex justify-between items-center">
									<span className="text-sm font-medium text-muted-foreground">
										Customer
									</span>
									<span className="text-sm">
										{paymentDetails.customer.name ||
											paymentDetails.customer.email}
									</span>
								</div>
							)}

							{/* Created Date */}
							<div className="flex justify-between items-center">
								<span className="text-sm font-medium text-muted-foreground">
									Created
								</span>
								<span className="text-sm">
									{formatDate(paymentDetails.created)}
								</span>
							</div>

							{/* Payment Method */}
							{paymentDetails.paymentMethod && (
								<div className="flex justify-between items-center">
									<span className="text-sm font-medium text-muted-foreground">
										Payment Method
									</span>
									<span className="text-sm">
										{paymentDetails.paymentMethod.type === "card"
											? `Card ending in ${paymentDetails.paymentMethod.card?.last4}`
											: paymentDetails.paymentMethod.type}
									</span>
								</div>
							)}
						</div>
					)}

					{/* Connection Status */}
					<div className="flex items-center justify-between text-xs text-muted-foreground border-t pt-2">
						<span>Connection: {connectionStatus}</span>
						{isPolling && <span>Auto-refresh enabled</span>}
					</div>
				</div>
			</CardContent>
		</Card>
	);
}
