"use client";

import Link from "next/link";
import {
	CreditCard,
	Shield,
	Zap,
	BarChart3,
	ArrowRight,
	CheckCircle,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";

const TITLE_TEXT = `
 ██████╗ ███████╗████████╗████████╗███████╗██████╗
 ██╔══██╗██╔════╝╚══██╔══╝╚══██╔══╝██╔════╝██╔══██╗
 ██████╔╝█████╗     ██║      ██║   █████╗  ██████╔╝
 ██╔══██╗██╔══╝     ██║      ██║   ██╔══╝  ██╔══██╗
 ██████╔╝███████╗   ██║      ██║   ███████╗██║  ██║
 ╚═════╝ ╚══════╝   ╚═╝      ╚═╝   ╚══════╝╚═╝  ╚═╝

 ████████╗    ███████╗████████╗ █████╗  ██████╗██╗  ██╗
 ╚══██╔══╝    ██╔════╝╚══██╔══╝██╔══██╗██╔════╝██║ ██╔╝
    ██║       ███████╗   ██║   ███████║██║     █████╔╝
    ██║       ╚════██║   ██║   ██╔══██║██║     ██╔═██╗
    ██║       ███████╗   ██║   ██║  ██║╚██████╗██║  ██╗
    ╚═╝       ╚══════╝   ╚═╝   ╚═╝  ╚═╝ ╚═════╝╚═╝  ╚═╝
 `;

export default function Home() {
	return (
		<div className="min-h-screen bg-gradient-to-br from-background via-background to-muted">
			<div className="container mx-auto max-w-6xl px-4 py-8">
				{/* Header */}
				<div className="text-center mb-12">
					<pre className="overflow-x-auto font-mono text-sm mb-6">
						{TITLE_TEXT}
					</pre>
					<h1 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
						Stripe Integration Demo
					</h1>
					<p className="text-xl text-muted-foreground max-w-2xl mx-auto">
						Complete payment processing demo with Stripe Elements, Express.js
						backend, and PostgreSQL
					</p>
				</div>

				{/* Quick Actions */}
				<div className="flex justify-center mb-12">
					<div className="flex flex-col sm:flex-row gap-4">
						<Button asChild size="lg" className="text-lg px-8">
							<Link href="/payment">
								<CreditCard className="mr-2 h-5 w-5" />
								Test Payment
							</Link>
						</Button>
						<Button variant="outline" size="lg" className="text-lg px-8">
							<Link href="/docs">
								View Documentation
								<ArrowRight className="ml-2 h-5 w-5" />
							</Link>
						</Button>
					</div>
				</div>

				{/* Feature Cards */}
				<div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
					<Card className="border-2 hover:border-primary/50 transition-colors">
						<CardHeader>
							<div className="flex items-center space-x-2">
								<CreditCard className="h-6 w-6 text-primary" />
								<CardTitle>Secure Payments</CardTitle>
							</div>
							<CardDescription>
								PCI-compliant payment processing with Stripe Elements
							</CardDescription>
						</CardHeader>
						<CardContent>
							<ul className="text-sm space-y-2 text-muted-foreground">
								<li className="flex items-center space-x-2">
									<CheckCircle className="h-4 w-4 text-green-600" />
									<span>Card & digital wallet support</span>
								</li>
								<li className="flex items-center space-x-2">
									<CheckCircle className="h-4 w-4 text-green-600" />
									<span>3D Secure authentication</span>
								</li>
								<li className="flex items-center space-x-2">
									<CheckCircle className="h-4 w-4 text-green-600" />
									<span>Real-time payment validation</span>
								</li>
							</ul>
						</CardContent>
					</Card>

					<Card className="border-2 hover:border-primary/50 transition-colors">
						<CardHeader>
							<div className="flex items-center space-x-2">
								<Shield className="h-6 w-6 text-green-600" />
								<CardTitle>Enterprise Security</CardTitle>
							</div>
							<CardDescription>
								Bank-level security for all transactions
							</CardDescription>
						</CardHeader>
						<CardContent>
							<ul className="text-sm space-y-2 text-muted-foreground">
								<li className="flex items-center space-x-2">
									<CheckCircle className="h-4 w-4 text-green-600" />
									<span>256-bit SSL encryption</span>
								</li>
								<li className="flex items-center space-x-2">
									<CheckCircle className="h-4 w-4 text-green-600" />
									<span>SOC 2 compliant</span>
								</li>
								<li className="flex items-center space-x-2">
									<CheckCircle className="h-4 w-4 text-green-600" />
									<span>Fraud detection tools</span>
								</li>
							</ul>
						</CardContent>
					</Card>

					<Card className="border-2 hover:border-primary/50 transition-colors">
						<CardHeader>
							<div className="flex items-center space-x-2">
								<Zap className="h-6 w-6 text-yellow-600" />
								<CardTitle>Lightning Fast</CardTitle>
							</div>
							<CardDescription>
								Optimized for speed and reliability
							</CardDescription>
						</CardHeader>
						<CardContent>
							<ul className="text-sm space-y-2 text-muted-foreground">
								<li className="flex items-center space-x-2">
									<CheckCircle className="h-4 w-4 text-green-600" />
									<span>&lt;2s average response time</span>
								</li>
								<li className="flex items-center space-x-2">
									<CheckCircle className="h-4 w-4 text-green-600" />
									<span>99.9% uptime guarantee</span>
								</li>
								<li className="flex items-center space-x-2">
									<CheckCircle className="h-4 w-4 text-green-600" />
									<span>Global payment network</span>
								</li>
							</ul>
						</CardContent>
					</Card>
				</div>

				{/* API Status Section */}
				<div className="grid lg:grid-cols-2 gap-8 mb-12">
					<Card>
						<CardHeader>
							<CardTitle className="flex items-center space-x-2">
								<BarChart3 className="h-5 w-5" />
								<span>System Status</span>
							</CardTitle>
							<CardDescription>
								Current system health and metrics
							</CardDescription>
						</CardHeader>
						<CardContent>
							<div className="space-y-4">
								<div className="flex justify-between items-center">
									<span className="text-sm font-medium">API Status</span>
									<span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
										Operational
									</span>
								</div>
								<div className="flex justify-between items-center">
									<span className="text-sm font-medium">Database</span>
									<span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
										Connected
									</span>
								</div>
								<div className="flex justify-between items-center">
									<span className="text-sm font-medium">
										Stripe Integration
									</span>
									<span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
										Active
									</span>
								</div>
								<div className="flex justify-between items-center">
									<span className="text-sm font-medium">Response Time</span>
									<span className="text-sm text-muted-foreground">127ms</span>
								</div>
							</div>
						</CardContent>
					</Card>

					<Card>
						<CardHeader>
							<CardTitle>Test Cards</CardTitle>
							<CardDescription>
								Use these test cards to simulate different payment scenarios
							</CardDescription>
						</CardHeader>
						<CardContent>
							<div className="space-y-3 text-sm">
								<div className="flex justify-between items-center p-2 bg-muted rounded">
									<span className="font-mono">4242 4242 4242 4242</span>
									<span className="text-green-600">Success</span>
								</div>
								<div className="flex justify-between items-center p-2 bg-muted rounded">
									<span className="font-mono">4000 0000 0000 0002</span>
									<span className="text-red-600">Declined</span>
								</div>
								<div className="flex justify-between items-center p-2 bg-muted rounded">
									<span className="font-mono">4000 0025 0000 3155</span>
									<span className="text-yellow-600">3D Secure</span>
								</div>
								<div className="flex justify-between items-center p-2 bg-muted rounded">
									<span className="font-mono">4000 0000 0000 9995</span>
									<span className="text-red-600">Insufficient Funds</span>
								</div>
							</div>
						</CardContent>
					</Card>
				</div>

				{/* Call to Action */}
				<div className="text-center">
					<h2 className="text-2xl font-bold mb-4">Ready to Test?</h2>
					<p className="text-muted-foreground mb-6">
						Try out the complete payment flow with real Stripe test cards
					</p>
					<Button asChild size="lg">
						<Link href="/payment">
							Start Testing Now
							<ArrowRight className="ml-2 h-5 w-5" />
						</Link>
					</Button>
				</div>
			</div>
		</div>
	);
}
