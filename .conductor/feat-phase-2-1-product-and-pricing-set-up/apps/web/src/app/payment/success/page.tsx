"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { CheckCircle, ArrowLeft, Receipt, Download } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";

interface PaymentDetails {
	id: string;
	amount: number;
	currency: string;
	status: string;
	customer?: {
		id: string;
		email: string;
		name?: string;
	};
	created: string;
}

export default function PaymentSuccessPage() {
	const searchParams = useSearchParams();
	const paymentIntentId = searchParams.get("payment_intent_id");
	const [paymentDetails, setPaymentDetails] = useState<PaymentDetails | null>(
		null
	);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		if (paymentIntentId) {
			// In a real app, you'd fetch payment details from your API
			// For now, we'll simulate it
			const fetchPaymentDetails = async () => {
				try {
					// Simulate API call
					await new Promise((resolve) => window.setTimeout(resolve, 1000));

					setPaymentDetails({
						id: paymentIntentId,
						amount: 1000, // $10.00
						currency: "usd",
						status: "succeeded",
						customer: {
							id: "cus_test",
							email: "<EMAIL>",
							name: "John Doe",
						},
						created: new Date().toISOString(),
					});
				} catch (error) {
					console.error("Error fetching payment details:", error);
				} finally {
					setLoading(false);
				}
			};

			fetchPaymentDetails();
		} else {
			setLoading(false);
		}
	}, [paymentIntentId]);

	const formatAmount = (amount: number, currency: string): string => {
		return new Intl.NumberFormat("en-US", {
			style: "currency",
			currency: currency.toUpperCase(),
		}).format(amount / 100);
	};

	const formatDate = (dateString: string): string => {
		return new Date(dateString).toLocaleDateString("en-US", {
			year: "numeric",
			month: "long",
			day: "numeric",
			hour: "2-digit",
			minute: "2-digit",
		});
	};

	if (loading) {
		return (
			<div className="min-h-screen flex items-center justify-center bg-background">
				<div className="text-center">
					<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
					<p className="text-muted-foreground">Loading payment details...</p>
				</div>
			</div>
		);
	}

	return (
		<div className="min-h-screen flex items-center justify-center bg-background p-4">
			<Card className="w-full max-w-md">
				<CardHeader className="text-center">
					<div className="flex justify-center mb-4">
						<div className="rounded-full bg-green-100 p-3">
							<CheckCircle className="h-8 w-8 text-green-600" />
						</div>
					</div>
					<CardTitle className="text-2xl text-green-600">
						Payment Successful!
					</CardTitle>
					<CardDescription>
						Your payment has been processed successfully
					</CardDescription>
				</CardHeader>
				<CardContent className="space-y-4">
					{paymentDetails && (
						<div className="space-y-3">
							<div className="flex justify-between items-center py-2 border-b">
								<span className="text-sm font-medium text-muted-foreground">
									Amount Paid
								</span>
								<span className="font-semibold">
									{formatAmount(paymentDetails.amount, paymentDetails.currency)}
								</span>
							</div>

							<div className="flex justify-between items-center py-2 border-b">
								<span className="text-sm font-medium text-muted-foreground">
									Payment ID
								</span>
								<span className="font-mono text-sm">
									{paymentDetails.id.slice(0, 8)}...
								</span>
							</div>

							{paymentDetails.customer && (
								<div className="flex justify-between items-center py-2 border-b">
									<span className="text-sm font-medium text-muted-foreground">
										Email
									</span>
									<span className="text-sm">
										{paymentDetails.customer.email}
									</span>
								</div>
							)}

							<div className="flex justify-between items-center py-2 border-b">
								<span className="text-sm font-medium text-muted-foreground">
									Date
								</span>
								<span className="text-sm">
									{formatDate(paymentDetails.created)}
								</span>
							</div>

							<div className="flex justify-between items-center py-2">
								<span className="text-sm font-medium text-muted-foreground">
									Status
								</span>
								<span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
									{paymentDetails.status}
								</span>
							</div>
						</div>
					)}

					<div className="space-y-2">
						<Button asChild className="w-full">
							<Link href="/">
								<ArrowLeft className="mr-2 h-4 w-4" />
								Return to Home
							</Link>
						</Button>

						<Button variant="outline" className="w-full">
							<Receipt className="mr-2 h-4 w-4" />
							View Receipt
						</Button>

						<Button variant="outline" className="w-full">
							<Download className="mr-2 h-4 w-4" />
							Download Receipt
						</Button>
					</div>

					<div className="text-center text-sm text-muted-foreground">
						<p>A confirmation email has been sent to your email address.</p>
						<p className="mt-1">
							Need help?{" "}
							<Link href="/support" className="text-primary hover:underline">
								Contact Support
							</Link>
						</p>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
