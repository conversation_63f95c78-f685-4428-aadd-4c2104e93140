"use client";

import { useRouter } from "next/navigation";
import { CreditCard, Shield, Lock } from "lucide-react";
import { PaymentForm } from "@/components/payment/payment-form";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";

export default function PaymentPage() {
	const router = useRouter();

	const handlePaymentSuccess = (_paymentIntentId: string) => {
		// Redirect to success page with payment intent ID
		router.push(`/payment/success?payment_intent_id=${_paymentIntentId}`);
	};

	const handlePaymentError = (_error: string) => {
		// Redirect to failure page with error details
		const errorData = window.btoa(JSON.stringify({ message: _error }));
		router.push(`/payment/failure?error=${encodeURIComponent(errorData)}`);
	};

	return (
		<div className="min-h-screen bg-gradient-to-br from-background to-muted p-4">
			<div className="max-w-6xl mx-auto">
				<div className="grid lg:grid-cols-3 gap-8 items-start">
					{/* Payment Form */}
					<div className="lg:col-span-2">
						<div className="mb-8 text-center">
							<h1 className="text-4xl font-bold mb-2">Secure Payment</h1>
							<p className="text-lg text-muted-foreground">
								Complete your payment safely and securely
							</p>
						</div>

						<PaymentForm
							amount={1000} // $10.00 default
							currency="usd"
							onSuccess={handlePaymentSuccess}
							onError={handlePaymentError}
						/>
					</div>

					{/* Security Information */}
					<div className="space-y-6">
						<Card>
							<CardHeader>
								<CardTitle className="flex items-center space-x-2">
									<Shield className="h-5 w-5 text-green-600" />
									<span>Security Features</span>
								</CardTitle>
							</CardHeader>
							<CardContent className="space-y-4">
								<div className="flex items-start space-x-3">
									<Lock className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
									<div>
										<h4 className="font-medium text-sm">PCI Compliant</h4>
										<p className="text-xs text-muted-foreground">
											Your card data is encrypted and processed securely through
											Stripe
										</p>
									</div>
								</div>

								<div className="flex items-start space-x-3">
									<Shield className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
									<div>
										<h4 className="font-medium text-sm">
											256-bit SSL Encryption
										</h4>
										<p className="text-xs text-muted-foreground">
											All data is transmitted securely using industry-standard
											encryption
										</p>
									</div>
								</div>

								<div className="flex items-start space-x-3">
									<CreditCard className="h-4 w-4 text-purple-600 mt-0.5 flex-shrink-0" />
									<div>
										<h4 className="font-medium text-sm">No Card Data Stored</h4>
										<p className="text-xs text-muted-foreground">
											We never store your complete card information on our
											servers
										</p>
									</div>
								</div>
							</CardContent>
						</Card>

						<Card>
							<CardHeader>
								<CardTitle className="text-lg">Accepted Cards</CardTitle>
								<CardDescription>
									We accept all major credit and debit cards
								</CardDescription>
							</CardHeader>
							<CardContent>
								<div className="grid grid-cols-2 gap-3">
									<div className="flex items-center space-x-2 text-sm">
										<div className="w-8 h-5 bg-blue-600 rounded text-white text-xs flex items-center justify-center font-bold">
											V
										</div>
										<span>Visa</span>
									</div>
									<div className="flex items-center space-x-2 text-sm">
										<div className="w-8 h-5 bg-red-600 rounded text-white text-xs flex items-center justify-center font-bold">
											MC
										</div>
										<span>Mastercard</span>
									</div>
									<div className="flex items-center space-x-2 text-sm">
										<div className="w-8 h-5 bg-blue-500 rounded text-white text-xs flex items-center justify-center font-bold">
											AMEX
										</div>
										<span>American Express</span>
									</div>
									<div className="flex items-center space-x-2 text-sm">
										<div className="w-8 h-5 bg-orange-500 rounded text-white text-xs flex items-center justify-center font-bold">
											DIS
										</div>
										<span>Discover</span>
									</div>
								</div>
							</CardContent>
						</Card>

						<Card>
							<CardHeader>
								<CardTitle className="text-lg">Need Help?</CardTitle>
							</CardHeader>
							<CardContent className="space-y-2">
								<p className="text-sm text-muted-foreground">
									If you encounter any issues during payment:
								</p>
								<ul className="text-sm text-muted-foreground space-y-1">
									<li>• Check your card details</li>
									<li>• Ensure sufficient funds</li>
									<li>• Try refreshing the page</li>
									<li>• Contact support if issues persist</li>
								</ul>
							</CardContent>
						</Card>
					</div>
				</div>
			</div>
		</div>
	);
}
