"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { <PERSON>Circle, ArrowLeft, RefreshCw, CreditCard } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";

interface PaymentError {
	message: string;
	code?: string;
	type?: string;
}

export default function PaymentFailurePage() {
	const searchParams = useSearchParams();
	const paymentIntentId = searchParams.get("payment_intent_id");
	const errorParam = searchParams.get("error");
	const [paymentError, setPaymentError] = useState<PaymentError | null>(null);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		if (errorParam) {
			try {
				// Try to parse the error parameter
				const errorData = JSON.parse(window.atob(errorParam));
				setPaymentError(errorData);
			} catch {
				// If parsing fails, use the raw error message
				setPaymentError({
					message: decodeURIComponent(errorParam),
				});
			}
		} else {
			// Default error if none provided
			setPaymentError({
				message: "Your payment could not be processed. Please try again.",
			});
		}
		setLoading(false);
	}, [errorParam]);

	const getErrorHelpText = (error: PaymentError): string => {
		switch (error.type) {
			case "card_error":
				return "Please check your card details and try again.";
			case "invalid_request_error":
				return "There was an issue with your request. Please try again.";
			case "api_error":
				return "There was a problem with our payment system. Please try again later.";
			case "authentication_error":
				return "There was an authentication error. Please contact support.";
			case "rate_limit_error":
				return "Too many requests. Please wait a moment and try again.";
			default:
				return "Please review your payment information and try again.";
		}
	};

	const getErrorTitle = (error: PaymentError): string => {
		switch (error.type) {
			case "card_error":
				return "Card Error";
			case "insufficient_funds":
				return "Insufficient Funds";
			case "expired_card":
				return "Expired Card";
			case "incorrect_cvc":
				return "Incorrect CVC";
			default:
				return "Payment Failed";
		}
	};

	if (loading) {
		return (
			<div className="min-h-screen flex items-center justify-center bg-background">
				<div className="text-center">
					<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
					<p className="text-muted-foreground">Loading error details...</p>
				</div>
			</div>
		);
	}

	const error = paymentError || { message: "An unknown error occurred" };

	return (
		<div className="min-h-screen flex items-center justify-center bg-background p-4">
			<Card className="w-full max-w-md">
				<CardHeader className="text-center">
					<div className="flex justify-center mb-4">
						<div className="rounded-full bg-red-100 p-3">
							<XCircle className="h-8 w-8 text-red-600" />
						</div>
					</div>
					<CardTitle className="text-2xl text-red-600">
						{getErrorTitle(error)}
					</CardTitle>
					<CardDescription>
						We couldn&apos;t process your payment
					</CardDescription>
				</CardHeader>
				<CardContent className="space-y-4">
					<div className="bg-red-50 border border-red-200 rounded-lg p-4">
						<p className="text-sm text-red-800 font-medium">{error.message}</p>
						<p className="text-sm text-red-600 mt-1">
							{getErrorHelpText(error)}
						</p>
					</div>

					{paymentIntentId && (
						<div className="text-sm text-muted-foreground">
							<p>
								Payment ID:{" "}
								<span className="font-mono">
									{paymentIntentId.slice(0, 8)}...
								</span>
							</p>
						</div>
					)}

					<div className="space-y-2">
						<Button asChild className="w-full">
							<Link href="/">
								<ArrowLeft className="mr-2 h-4 w-4" />
								Return to Home
							</Link>
						</Button>

						<Button variant="outline" className="w-full" asChild>
							<Link href="/payment">
								<CreditCard className="mr-2 h-4 w-4" />
								Try Again
							</Link>
						</Button>

						<Button variant="outline" className="w-full">
							<RefreshCw className="mr-2 h-4 w-4" />
							Use Different Card
						</Button>
					</div>

					<div className="text-center text-sm text-muted-foreground">
						<p>
							Still having trouble?{" "}
							<Link href="/support" className="text-primary hover:underline">
								Contact Support
							</Link>
						</p>
						<p className="mt-1 text-xs">
							Reference ID: {paymentIntentId || "N/A"}
						</p>
					</div>

					{/* Common Issues */}
					<div className="border-t pt-4">
						<h4 className="font-medium text-sm mb-2">Common Issues:</h4>
						<ul className="text-xs text-muted-foreground space-y-1">
							<li>• Check your card number and expiration date</li>
							<li>• Ensure you have sufficient funds</li>
							<li>• Verify your CVC/security code</li>
							<li>• Try a different payment method</li>
						</ul>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
