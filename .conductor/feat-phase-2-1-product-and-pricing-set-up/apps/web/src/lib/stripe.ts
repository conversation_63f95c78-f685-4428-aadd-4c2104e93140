import {
	loadStripe,
	type Stripe,
	type StripeElementsOptions,
} from "@stripe/stripe-js";

// Make sure to call `loadStripe` outside of a component's render to avoid
// recreating the `Stripe` object on every render.
let stripePromise: Promise<Stripe | null>;

export const getStripe = () => {
	if (!stripePromise) {
		const publishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;

		if (!publishableKey) {
			throw new Error(
				"NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY is not defined in environment variables"
			);
		}

		stripePromise = loadStripe(publishableKey);
	}
	return stripePromise;
};

// Default theme options for Stripe Elements
export const getStripeElementsOptions = (
	theme: "light" | "dark" = "light"
): StripeElementsOptions => {
	return {
		appearance: {
			theme: "stripe",
			variables: {
				colorPrimary: theme === "dark" ? "#ffffff" : "#000000",
				colorBackground: theme === "dark" ? "#1a1a1a" : "#ffffff",
				colorText: theme === "dark" ? "#ffffff" : "#000000",
				colorDanger: "#df1b41",
				fontFamily: '"Inter", system-ui, sans-serif',
				spacingUnit: "4px",
				borderRadius: "8px",
				// Custom CSS for better integration
				fontSizeBase: "14px",
				fontWeightNormal: "400",
				fontWeightBold: "600",
			},
			rules: {
				".Input": {
					border: theme === "dark" ? "1px solid #333" : "1px solid #e1e1e1",
					boxShadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1)",
				},
				".Input:focus": {
					border: theme === "dark" ? "1px solid #555" : "1px solid #635bff",
					boxShadow: "0 1px 3px 0 rgba(99, 91, 255, 0.1)",
				},
				".Input--invalid": {
					border: "1px solid #df1b41",
				},
				".Tab": {
					border: theme === "dark" ? "1px solid #333" : "1px solid #e1e1e1",
					backgroundColor: theme === "dark" ? "#1a1a1a" : "#ffffff",
				},
				".Tab:hover": {
					backgroundColor: theme === "dark" ? "#252525" : "#f8f9fa",
				},
				".Tab--selected": {
					border: theme === "dark" ? "1px solid #555" : "1px solid #635bff",
					backgroundColor: theme === "dark" ? "#1a1a1a" : "#ffffff",
				},
			},
		},
		loader: "auto",
	};
};

// Utility functions for formatting amounts
export const formatAmountForDisplay = (
	amount: number,
	currency: string
): string => {
	return new Intl.NumberFormat("en-US", {
		style: "currency",
		currency: currency.toUpperCase(),
	}).format(amount / 100); // Stripe amounts are in cents
};

export const formatAmountForStripe = (
	amount: number,
	currency: string
): number => {
	// Some currencies are zero-decimal (e.g., JPY), others use cents
	const zeroDecimalCurrencies = ["jpy", "krw", "vnd"];
	const isZeroDecimal = zeroDecimalCurrencies.includes(currency.toLowerCase());

	return isZeroDecimal ? amount : Math.round(amount * 100);
};

// Validate card number format (basic client-side validation)
export const isValidCardNumber = (cardNumber: string): boolean => {
	// Remove spaces and hyphens
	const cleaned = cardNumber.replace(/[\s-]/g, "");

	// Check if it's all digits and has reasonable length
	if (!/^\d+$/.test(cleaned) || cleaned.length < 13 || cleaned.length > 19) {
		return false;
	}

	// Simple Luhn algorithm check
	let sum = 0;
	let shouldDouble = false;

	for (let i = cleaned.length - 1; i >= 0; i--) {
		let digit = parseInt(cleaned[i]);

		if (shouldDouble) {
			digit *= 2;
			if (digit > 9) {
				digit -= 9;
			}
		}

		sum += digit;
		shouldDouble = !shouldDouble;
	}

	return sum % 10 === 0;
};

// Get card brand from number
export const getCardBrand = (cardNumber: string): string => {
	const cleaned = cardNumber.replace(/[\s-]/g, "");

	if (/^4/.test(cleaned)) return "visa";
	if (/^5[1-5]/.test(cleaned)) return "mastercard";
	if (/^3[47]/.test(cleaned)) return "amex";
	if (/^6(?:011|5)/.test(cleaned)) return "discover";
	if (/^(?:2131|1800|35)/.test(cleaned)) return "jcb";
	if (/^30[0-5]/.test(cleaned)) return "diners";

	return "unknown";
};

// Common Stripe test card numbers
export const TEST_CARDS = {
	visa: "****************",
	visaDebit: "****************",
	mastercard: "****************",
	amex: "***************",
	declined: "****************",
	insufficientFunds: "****************",
	fraudulent: "****************",
	require3DS: "****************",
} as const;
