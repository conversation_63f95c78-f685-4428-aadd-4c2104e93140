// API client configuration and utilities
import { useState } from "react";

const API_BASE_URL =
	process.env.NEXT_PUBLIC_SERVER_URL || "http://localhost:3000";

// API Response types
export interface ApiResponse<T = any> {
	data?: T;
	error?: string;
	message?: string;
	requestId?: string;
}

export interface ApiError {
	message: string;
	status: number;
	requestId?: string;
	details?: any;
}

// Custom error class
export class ApiClientError extends Error {
	status: number;
	requestId?: string;
	details?: any;

	constructor(
		message: string,
		status: number,
		requestId?: string,
		details?: any
	) {
		super(message);
		this.name = "ApiClientError";
		this.status = status;
		this.requestId = requestId;
		this.details = details;
	}
}

// Generic API client class
class ApiClient {
	private baseUrl: string;

	constructor(baseUrl: string) {
		this.baseUrl = baseUrl;
	}

	private async makeRequest<T>(
		endpoint: string,
		options: RequestInit = {}
	): Promise<T> {
		const url = `${this.baseUrl}${endpoint}`;

		const config: RequestInit = {
			headers: {
				"Content-Type": "application/json",
				...options.headers,
			},
			...options,
		};

		try {
			const response = await fetch(url, config);
			const data: ApiResponse<T> = await response.json();

			if (!response.ok) {
				throw new ApiClientError(
					data.error || data.message || `HTTP ${response.status}`,
					response.status,
					data.requestId,
					data
				);
			}

			return data.data || (data as T);
		} catch (error) {
			if (error instanceof ApiClientError) {
				throw error;
			}

			// Network or parsing errors
			throw new ApiClientError(
				error instanceof Error ? error.message : "Network error",
				0
			);
		}
	}

	async get<T>(
		endpoint: string,
		params?: Record<string, string | number>
	): Promise<T> {
		const url = new URL(`${this.baseUrl}${endpoint}`);
		if (params) {
			Object.entries(params).forEach(([key, value]) => {
				url.searchParams.append(key, String(value));
			});
		}

		return this.makeRequest<T>(url.pathname + url.search, {
			method: "GET",
		});
	}

	async post<T>(endpoint: string, data?: any): Promise<T> {
		return this.makeRequest<T>(endpoint, {
			method: "POST",
			body: data ? JSON.stringify(data) : undefined,
		});
	}

	async put<T>(endpoint: string, data?: any): Promise<T> {
		return this.makeRequest<T>(endpoint, {
			method: "PUT",
			body: data ? JSON.stringify(data) : undefined,
		});
	}

	async delete<T>(endpoint: string): Promise<T> {
		return this.makeRequest<T>(endpoint, {
			method: "DELETE",
		});
	}

	// Raw request method for custom handling (e.g., webhooks with raw body)
	async raw(endpoint: string, options: RequestInit): Promise<Response> {
		const url = `${this.baseUrl}${endpoint}`;
		return fetch(url, options);
	}
}

// Initialize API client
export const apiClient = new ApiClient(API_BASE_URL);

// Payment-specific API functions
export interface CreatePaymentIntentRequest {
	amount: number;
	currency: string;
	automatic_payment_methods?: {
		enabled: boolean;
	};
	customer?: {
		email: string;
		name?: string;
	};
	metadata?: Record<string, string>;
}

export interface CreatePaymentIntentResponse {
	clientSecret: string;
	paymentIntentId: string;
	customerId?: string;
}

export interface ConfirmPaymentRequest {
	paymentIntentId: string;
	paymentMethodId?: string;
}

export interface ConfirmPaymentResponse {
	success: boolean;
	paymentIntent: any; // Stripe PaymentIntent object
	error?: string;
}

export interface PaymentDetails {
	id: string;
	amount: number;
	currency: string;
	status: string;
	customer?: {
		id: string;
		email: string;
		name?: string;
	};
	created: string;
	paymentMethod?: any;
}

// Payment API methods
export const paymentApi = {
	/**
	 * Create a payment intent
	 */
	createPaymentIntent: (
		data: CreatePaymentIntentRequest
	): Promise<CreatePaymentIntentResponse> =>
		apiClient.post("/api/payments/create-intent", data),

	/**
	 * Confirm a payment
	 */
	confirmPayment: (
		data: ConfirmPaymentRequest
	): Promise<ConfirmPaymentResponse> =>
		apiClient.post("/api/payments/confirm", data),

	/**
	 * Get payment details
	 */
	getPayment: (paymentId: string): Promise<PaymentDetails> =>
		apiClient.get(`/api/payments/${paymentId}`),

	/**
	 * Get payment history for a customer
	 */
	getPaymentHistory: (
		customerId: string,
		params?: { limit?: number; offset?: number }
	): Promise<PaymentDetails[]> =>
		apiClient.get(`/api/customers/${customerId}/payments`, params),
};

// Customer API methods
export interface CreateCustomerRequest {
	email: string;
	name?: string;
	phone?: string;
	address?: {
		line1: string;
		line2?: string;
		city: string;
		state?: string;
		postal_code: string;
		country: string;
	};
}

export interface CustomerDetails {
	id: string;
	stripeCustomerId: string;
	email: string;
	name?: string;
	phone?: string;
	created: string;
	updated: string;
}

export const customerApi = {
	/**
	 * Create a new customer
	 */
	createCustomer: (data: CreateCustomerRequest): Promise<CustomerDetails> =>
		apiClient.post("/api/customers", data),

	/**
	 * Get customer details
	 */
	getCustomer: (customerId: string): Promise<CustomerDetails> =>
		apiClient.get(`/api/customers/${customerId}`),

	/**
	 * Update customer
	 */
	updateCustomer: (
		customerId: string,
		data: Partial<CreateCustomerRequest>
	): Promise<CustomerDetails> =>
		apiClient.put(`/api/customers/${customerId}`, data),
};

// Utility functions
export const handleApiError = (error: unknown): string => {
	if (error instanceof ApiClientError) {
		return error.message;
	}

	if (error instanceof Error) {
		return error.message;
	}

	return "An unexpected error occurred";
};

// React hook for API loading states
export const useApiCall = <T>() => {
	const [data, setData] = useState<T | null>(null);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const execute = async (apiCall: () => Promise<T>) => {
		setLoading(true);
		setError(null);

		try {
			const result = await apiCall();
			setData(result);
			return result;
		} catch (err) {
			const errorMessage = handleApiError(err);
			setError(errorMessage);
			throw err;
		} finally {
			setLoading(false);
		}
	};

	const reset = () => {
		setData(null);
		setError(null);
		setLoading(false);
	};

	return { data, loading, error, execute, reset };
};

// Helper to check if we're in development mode
export const isDevelopment = () => {
	return process.env.NODE_ENV === "development";
};

// Helper to log API calls in development
export const logApiCall = (method: string, endpoint: string, data?: any) => {
	if (isDevelopment()) {
		console.warn(`🌐 API ${method.toUpperCase()} ${endpoint}`, data || "");
	}
};
