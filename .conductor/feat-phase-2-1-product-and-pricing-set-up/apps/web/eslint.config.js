import baseConfig from "../../eslint.config.js";
import reactPlugin from "eslint-plugin-react";
import reactHooksPlugin from "eslint-plugin-react-hooks";
import jsxA11yPlugin from "eslint-plugin-jsx-a11y";

export default [
	...baseConfig,
	{
		ignores: [
			"**/*.config.*",
			"**/*.mjs",
			"postcss.config.mjs",
			"next.config.*",
		],
	},
	{
		files: ["**/*.{js,jsx,ts,tsx}"],
		languageOptions: {
			parserOptions: {
				ecmaFeatures: {
					jsx: true,
				},
				project: "./tsconfig.json",
			},
			globals: {
				React: "readonly",
				window: "readonly",
				document: "readonly",
				navigator: "readonly",
				localStorage: "readonly",
				sessionStorage: "readonly",
				fetch: "readonly",
			},
		},
		plugins: {
			react: reactPlugin,
			"react-hooks": reactHooksPlugin,
			"jsx-a11y": jsxA11yPlugin,
		},
		settings: {
			react: {
				version: "detect",
			},
		},
		rules: {
			// React specific rules
			"react/react-in-jsx-scope": "off", // Not needed in Next.js 13+
			"react/prop-types": "off", // Using TypeScript for prop validation
			"react/display-name": "off",
			"react/no-unescaped-entities": "warn",

			// React Hooks rules
			"react-hooks/rules-of-hooks": "error",
			"react-hooks/exhaustive-deps": "warn",

			// Accessibility rules
			"jsx-a11y/alt-text": "warn",
			"jsx-a11y/anchor-is-valid": "off", // Next.js Link component handles this

			// Import rules for React
			"import/order": [
				"error",
				{
					groups: [
						"builtin",
						"external",
						"internal",
						"parent",
						"sibling",
						"index",
					],
					"newlines-between": "never",
					alphabetize: {
						order: "asc",
						caseInsensitive: true,
					},
					pathGroups: [
						{
							pattern: "react",
							group: "external",
							position: "before",
						},
						{
							pattern: "next/**",
							group: "external",
							position: "before",
						},
						{
							pattern: "@/**",
							group: "internal",
						},
					],
					pathGroupsExcludedImportTypes: ["react"],
				},
			],

			// Console rules (more lenient for development)
			"no-console": ["warn", { allow: ["warn", "error"] }],
		},
	},
];
