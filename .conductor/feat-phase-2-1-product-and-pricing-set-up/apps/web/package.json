{"name": "web", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack --port=3001", "build": "next build", "start": "next start", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 15", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@stripe/react-stripe-js": "^3.9.2", "@stripe/stripe-js": "^7.9.0", "@tanstack/react-form": "^1.12.3", "@tanstack/react-query": "^5.85.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.487.0", "next": "15.5.0", "next-themes": "^0.4.6", "radix-ui": "^1.4.2", "react": "^19.0.0", "react-dom": "^19.0.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.4", "zod": "^4.0.2"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.10", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4.1.10", "typescript": "^5", "@tanstack/react-query-devtools": "^5.85.5", "eslint-config-next": "15.5.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-jsx-a11y": "^6.10.2"}}