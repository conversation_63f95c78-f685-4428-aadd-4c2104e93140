# Agents Coding Guidelines

## Build/Test Commands

- `bun install` - Install dependencies (uses Bun package manager)
- `bun dev` - Start all apps (web on :3001, server on :3000)
- `bun build` - Build all applications
- `bun check-types` - TypeScript compilation check
- `bun lint` - ESLint check (max 15 warnings allowed)
- `bun format` - Format with Prettier
- `turbo -F server <command>` - Run command on server only
- `turbo -F web <command>` - Run command on web only

## Code Style

- **Formatting**: Prettier with tabs, 80 char width, double quotes, trailing commas
- **Imports**: Alphabetical order, no newlines between groups, no duplicates
- **Types**: TypeScript strict mode, prefer explicit types, underscore prefix for unused vars
- **Naming**: camelCase variables/functions, PascalCase components/types, kebab-case files
- **Error Handling**: Use Zod validation, proper Express error middleware, typed errors

## Key Patterns

- Drizzle ORM with Zod schemas for database operations
- shadcn/ui components with TailwindCSS styling
- Stripe amounts always in cents, status enums match Stripe exactly
- Rate limiting on payment endpoints, CORS configured for localhost:3001
