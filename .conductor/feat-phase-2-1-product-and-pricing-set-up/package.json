{"name": "stripe-demonstration", "private": true, "type": "module", "workspaces": ["apps/*", "packages/*"], "scripts": {"prepare": "husky", "dev": "turbo dev", "build": "turbo build", "check-types": "turbo check-types", "lint": "turbo lint", "lint:fix": "turbo lint:fix", "format": "turbo format", "format:check": "turbo format:check", "dev:native": "turbo -F native dev", "dev:web": "turbo -F web dev", "dev:server": "turbo -F server dev", "db:push": "turbo -F server db:push", "db:studio": "turbo -F server db:studio", "db:generate": "turbo -F server db:generate", "db:migrate": "turbo -F server db:migrate"}, "dependencies": {}, "devDependencies": {"turbo": "^2.5.4", "husky": "^9.1.7", "lint-staged": "^16.1.2", "eslint": "^9.17.0", "@eslint/js": "^9.17.0", "@typescript-eslint/eslint-plugin": "^8.19.1", "@typescript-eslint/parser": "^8.19.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-import": "^2.31.0", "prettier": "^3.4.2"}, "lint-staged": {"apps/web/**/*.{js,jsx,ts,tsx}": ["cd apps/web && npx eslint . --ext ts,tsx --fix", "prettier --write"], "apps/server/**/*.{js,ts}": ["cd apps/server && npx eslint . --ext ts --fix", "prettier --write"], "**/*.{json,md,yml,yaml}": ["prettier --write"]}, "packageManager": "bun@1.2.21"}